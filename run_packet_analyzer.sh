#!/bin/bash

# <PERSON><PERSON><PERSON> tra xem script có đư<PERSON><PERSON> chạy với quyền root không
if [ "$EUID" -ne 0 ]; then
    echo "Vui lòng chạy script này với quyền root (sudo)"
    exit 1
fi

# Đường dẫn đến socket
SOCKET_PATH="/tmp/packet_capture.sock"

# Xóa socket cũ nếu tồn tại
if [ -e "$SOCKET_PATH" ]; then
    rm "$SOCKET_PATH"
fi

# Khởi động quá trình bắt gói tin trong nền
python3 packet_capture.py -s "$SOCKET_PATH" &
CAPTURE_PID=$!

# Đợi socket được tạo
while [ ! -e "$SOCKET_PATH" ]; do
    sleep 0.1
done

# Khởi động giao diện với quyền người dùng thường
sudo -u $SUDO_USER python3 packet_gui.py -s "$SOCKET_PATH"

# Khi giao diện đóng, dừng quá trình bắt gói tin
kill $CAPTURE_PID 2>/dev/null