
import struct
import zlib

def read2(data,offset):
    return struct.unpack("<H", data[offset:offset+2])[0]

def retrieve_original_package2(packet_buffer: bytes, original_length: int) -> bytes:
    try:
        decompressed_data = zlib.decompress(packet_buffer)
        if len(decompressed_data) < original_length:
            raise ValueError("Decompressed data is shorter than expected original length.")
        return decompressed_data[:original_length]
    except zlib.error as e:
        raise ValueError(f"Decompression failed: {e}")
def retrieve_original_package(packet_buffer: bytes, original_length: int) -> bytes:
    print(packet_buffer.hex())
    original_package = bytearray()
    i = 0
    while i < len(packet_buffer):
        if packet_buffer[i] < 32:
            literal_length = packet_buffer[i] + 1
            i += 1
            for j in range(literal_length):
                if i < len(packet_buffer):
                    original_package.append(packet_buffer[i])
                    i += 1
                else:
                    break
        else:
            if (packet_buffer[i] & 0xE0) == 224:
                repeat_length = (packet_buffer[i] & 0x1F) + 7 + 2 + packet_buffer[i + 1]
                offset = packet_buffer[i + 2] + 1
                i += 3
            else:
                repeat_length = (packet_buffer[i] >> 5) + 2
                offset = packet_buffer[i + 1] + 1
                i += 2

            for j in range(repeat_length):
                if len(original_package) - offset >= 0:
                    original_package.append(original_package[len(original_package) - offset])
                else:
                    raise ValueError("Invalid offset")

    return bytes(original_package[:original_length])

def decompress_packet(packet: bytes) -> bytes:
    original_length = read2(packet, 14)
    compressed_data = packet[16:read2(packet, 12) + 16]
    decompressed_data = retrieve_original_package(compressed_data, original_length)
    return bytearray.fromhex('aa550000') + decompressed_data + bytearray.fromhex('55aa')
import re
def process_file(input_file, output_file):
    with open(input_file, 'r') as infile, open(output_file, 'w') as outfile:
        for line in infile:
            try:
                byte_data = bytearray.fromhex(line)
                result_data = decompress_packet(byte_data)
                outfile.write(f"{result_data.hex()}\n")
            except ValueError as e:
                print(f"Error processing line: {line.strip()}. Error: {str(e)}")

def process_file2(input_file, output_file):
    # Compile a regex pattern to match strings that start with 'aa55' and end with '55aa'
    pattern = re.compile(r'aa55.*?55aa', re.IGNORECASE)

    with open(input_file, 'r') as infile, open(output_file, 'w') as outfile:
        for line in infile:
            # Split the line by ':' and strip whitespace
            parts = [part.strip() for part in line.split(':')]
            if len(parts) >= 2:
                key, data = parts[0], parts[1]
                print(f"Processing line: {data}")

                # Use regex to find all matches in the data
                matches = pattern.findall(data.replace(' ', ''))
                outfile.write(f"{matches[0]}\n")
# Example usage
input_filename = '1_out.txt'
output_filename = '1_dec.txt'
process_file(input_filename, output_filename)

