
import struct
import zlib


def read2(data, offset):
    return struct.unpack("<H", data[offset:offset+2])[0]


def retrieve_original_package2(packet_buffer: bytes, original_length: int) -> bytes:
    try:
        # Use zlib to decompress the packet buffer
        decompressed_data = zlib.decompress(packet_buffer)
        # Ensure the decompressed data matches the expected original length
        if len(decompressed_data) < original_length:
            raise ValueError(
                "Decompressed data is shorter than expected original length.")
        return decompressed_data[:original_length]
    except zlib.error as e:
        raise ValueError(f"Decompression failed: {e}")


def retrieve_original_package(packet_buffer: bytes, original_length: int) -> bytes:
    print(packet_buffer.hex())
    original_package = bytearray()
    i = 0
    while i < len(packet_buffer):
        if packet_buffer[i] < 32:
            literal_length = packet_buffer[i] + 1
            i += 1
            for j in range(literal_length):
                if i < len(packet_buffer):
                    original_package.append(packet_buffer[i])
                    i += 1
                else:
                    break
        else:
            if (packet_buffer[i] & 0xE0) == 224:
                repeat_length = (
                    packet_buffer[i] & 0x1F) + 7 + 2 + packet_buffer[i + 1]
                offset = packet_buffer[i + 2] + 1
                i += 3
            else:
                repeat_length = (packet_buffer[i] >> 5) + 2
                offset = packet_buffer[i + 1] + 1
                i += 2

            for j in range(repeat_length):
                if len(original_package) - offset >= 0:
                    original_package.append(
                        original_package[len(original_package) - offset])
                else:
                    raise ValueError("Invalid offset")

    return bytes(original_package[:original_length])


def decompress_packet(packet: bytes) -> bytes:
    original_length = read2(packet, 14)
    compressed_data = packet[16:read2(packet, 12) + 16]
    decompressed_data = retrieve_original_package(
        compressed_data, original_length)
    return bytearray.fromhex('aa550000') + decompressed_data + bytearray.fromhex('55aa')

# packet = "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"
#
# result = decompress_packet(bytearray.fromhex(packet))
# print(result.hex())
