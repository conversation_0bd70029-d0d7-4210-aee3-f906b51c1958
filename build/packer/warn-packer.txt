
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pyimod02_importers - imported by C:\Python312\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), netrc (delayed, conditional), getpass (delayed)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional)
missing module named posix - imported by os (conditional, optional), shutil (conditional), importlib._bootstrap_external (conditional), posixpath (optional)
missing module named resource - imported by posix (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by scapy.utils (delayed, conditional, optional), getpass (optional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named fcntl - imported by subprocess (optional), scapy.arch.linux (top-level), scapy.arch.unix (top-level), scapy.arch.bpf.core (top-level), scapy.arch.bpf.supersocket (top-level), scapy.arch.libpcap (conditional), scapy.utils (delayed, conditional, optional), scapy.layers.tuntap (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named StringIO - imported by scapy.libs.six (conditional), scapy.compat (conditional)
missing module named matplotlib - imported by scapy.libs.matplot (optional), scapy.layers.inet (delayed)
missing module named 'matplotlib.collections' - imported by scapy.layers.inet (delayed)
missing module named cartopy - imported by scapy.layers.inet (delayed, optional)
missing module named 'geoip2.errors' - imported by scapy.layers.inet (delayed, optional)
missing module named geoip2 - imported by scapy.layers.inet (delayed, optional)
missing module named vpython - imported by scapy.layers.inet (delayed)
missing module named 'matplotlib.lines' - imported by scapy.libs.matplot (optional)
missing module named 'scapy.libs.six.moves' - imported by scapy.base_classes (top-level), scapy.utils (top-level), scapy.interfaces (top-level), scapy.arch.windows (top-level), scapy.autorun (top-level), scapy.scapypipes (top-level)
missing module named 'cryptography.hazmat' - imported by scapy.layers.tls.crypto.pkcs1 (conditional), scapy.layers.tls.crypto.hkdf (conditional), scapy.layers.tls.cert (conditional), scapy.layers.tls.crypto.groups (conditional), scapy.layers.tls.keyexchange (conditional), scapy.layers.tls.crypto.cipher_aead (conditional), scapy.layers.tls.crypto.cipher_block (conditional), scapy.layers.tls.crypto.cipher_stream (conditional), scapy.layers.tls.keyexchange_tls13 (conditional), scapy.layers.tls.handshake (conditional), scapy.config (delayed, optional), scapy.layers.ntlm (delayed, conditional), scapy.layers.dot11 (conditional), scapy.layers.ipsec (conditional), scapy.layers.smb2 (delayed, conditional), scapy.layers.tls.automaton_srv (conditional)
missing module named cryptography - imported by scapy.layers.tls.crypto.pkcs1 (conditional), scapy.config (delayed, optional)
missing module named 'cryptography.utils' - imported by scapy.layers.tls.crypto.cipher_block (conditional), scapy.error (conditional, optional), scapy.layers.ipsec (conditional, optional)
missing module named 'cryptography.exceptions' - imported by scapy.layers.tls.crypto.cipher_aead (conditional), scapy.layers.ipsec (conditional)
missing module named prompt_toolkit - imported by scapy.packet (delayed, conditional, optional), scapy.layers.kerberos (delayed, conditional, optional)
missing module named 'Crypto.Protocol' - imported by scapy.libs.rfc3961 (optional)
missing module named 'Crypto.Hash' - imported by scapy.libs.rfc3961 (optional)
missing module named Crypto - imported by scapy.libs.rfc3961 (optional)
missing module named 'Cryptodome.Protocol' - imported by scapy.libs.rfc3961 (optional)
missing module named 'Cryptodome.Hash' - imported by scapy.libs.rfc3961 (optional)
missing module named Cryptodome - imported by scapy.libs.rfc3961 (optional)
missing module named lzw - imported by scapy.layers.http (delayed, conditional, optional)
missing module named zstandard - imported by scapy.layers.http (optional)
missing module named brotli - imported by scapy.layers.http (optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named scapy.arch.get_if_raw_hwaddr - imported by scapy.arch (delayed), scapy.arch.common (delayed, conditional), scapy.layers.dhcp (top-level), scapy.layers.dhcp6 (top-level)
missing module named traitlets - imported by scapy.main (delayed, conditional, optional)
missing module named IPython - imported by scapy.arch.windows (delayed, optional), scapy.main (delayed, conditional, optional)
missing module named readline - imported by code (delayed, conditional, optional)
missing module named mock - imported by scapy.utils (delayed, optional)
missing module named 'prompt_toolkit.formatted_text' - imported by scapy.packet (delayed, conditional)
missing module named 'prompt_toolkit.shortcuts' - imported by scapy.packet (delayed, conditional)
missing module named pyx - imported by scapy.base_classes (optional), scapy.plist (delayed), scapy.libs.test_pyx (optional), scapy.packet (optional)
missing module named 'IPython.terminal' - imported by scapy.themes (delayed, optional)
missing module named colorama - imported by scapy.error (conditional, optional)
missing module named __pypy__ - imported by scapy.config (delayed, optional)
missing module named __builtin__ - imported by scapy.config (delayed, optional)
missing module named 'scapy.nmap' - imported by scapy.config (conditional)
