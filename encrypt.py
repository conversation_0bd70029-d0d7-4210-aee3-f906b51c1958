# import struct


import struct


def encrypt_packet(data, key):
    def compute_key_segment(value):
        eax = value
        ecx = eax ^ 0x3D0000
        ecx = (ecx >> 16) ^ eax
        ecx = (ecx + ecx * 8) & 0xFFFFFFFF
        eax = ecx
        eax = (eax >> 4) ^ ecx
        eax = (eax * 0x73C2EB2D) & 0xFFFFFFFF
        ebx = eax
        ebx = (ebx >> 15) ^ eax
        return ebx

    packet_size = len(data)
    if packet_size < 1:
        return data

    key_segment = compute_key_segment(key)
    encrypted_data = bytearray(data)

    if packet_size >= 4:
        full_blocks = packet_size // 4
        remainder = packet_size % 4
        for i in range(full_blocks):
            offset = i * 4
            segment = int.from_bytes(encrypted_data[offset:offset+4], 'little')
            encrypted_segment = segment ^ key_segment
            encrypted_data[offset:offset +
                           4] = encrypted_segment.to_bytes(4, 'little')
        packet_size -= full_blocks * 4

    if packet_size == 1:
        encrypted_data[-1] ^= key_segment & 0xFF
    elif packet_size == 2:
        encrypted_data[-2:] = (int.from_bytes(encrypted_data[-2:],
                               'little') ^ (key_segment & 0xFFFF)).to_bytes(2, 'little')
    elif packet_size == 3:
        key_segment &= 0xFFFFFF
        encrypted_data[-3:] = (int.from_bytes(encrypted_data[-3:],
                               'little') ^ key_segment).to_bytes(3, 'little')

    return bytes(encrypted_data)


def compute_key_segment(value):
    value = struct.unpack('h', struct.pack('H', value & 0xFFFF))[0]

    eax = value & 0xFFFFFFFF
    ecx = (eax ^ 0x3D0000) & 0xFFFFFFFF
    ecx = ((ecx >> 16) ^ eax) & 0xFFFFFFFF
    ecx = (ecx + ecx * 8) & 0xFFFFFFFF
    eax = ecx
    eax = ((eax >> 4) ^ ecx) & 0xFFFFFFFF
    eax = (eax * 0x73C2EB2D) & 0xFFFFFFFF
    ebx = eax
    ebx = ((ebx >> 15) ^ eax) & 0xFFFFFFFF

    return ebx


def decrypt_packet(encrypted_data, key):
    if len(encrypted_data) < 1:
        print("length <1")
        return encrypted_data

    key_segment = compute_key_segment(key)
    decrypted_data = bytearray(encrypted_data)
    packet_size = len(encrypted_data)

    if packet_size >= 4:
        full_blocks = packet_size // 4
        for i in range(full_blocks):
            offset = i * 4
            segment = int.from_bytes(
                decrypted_data[offset:offset + 4], byteorder='little')
            decrypted_segment = segment ^ key_segment
            decrypted_data[offset:offset +
                           4] = decrypted_segment.to_bytes(4, byteorder='little')
        packet_size -= full_blocks * 4

    if packet_size == 1:
        decrypted_data[-1] ^= key_segment & 0xFF
    elif packet_size == 2:
        two_bytes = int.from_bytes(decrypted_data[-2:], byteorder='little')
        decrypted_data[-2:] = (two_bytes ^ (key_segment & 0xFFFF)
                               ).to_bytes(2, byteorder='little')
    elif packet_size == 3:
        three_bytes = int.from_bytes(decrypted_data[-3:], byteorder='little')
        decrypted_data[-3:] = (three_bytes ^ (key_segment &
                               0xFFFFFF)).to_bytes(3, byteorder='little')

    return decrypted_data


def extract_key(packet):
    if len(packet) < 6:
        raise ValueError("Packet too short to extract key")
    key = struct.unpack_from('<h', packet, 4)[0]
    return key


def decrypt_data(packet):
    try:
        key = extract_key(packet)
        length = packet[2:4]
        encrypted_data = packet[6:-2]
        decrypted_data = decrypt_packet(encrypted_data, key)
        data = decrypted_data.hex()
        data = 'aa55' + length.hex() + data + '55aa'
        return bytearray.fromhex(data)
    except Exception as e:
        print(f"Error processing packet: {e}")
        return packet


def encrypt_data(packet):
    try:
        key = extract_key(packet)
        length = packet[2:4]
        encrypted_data = encrypt_packet(packet[6:-2], key)
        data = encrypted_data.hex()
        data = 'aa55' + length.hex() + data + '55aa'
        return bytearray.fromhex(data)
    except Exception as e:
        print(f"Error processing packet: {e}")
        return packet


hex = "AA5582008116C29518A0C39560A0BBF76CC5B1E12991F19518A0C29518A0C29518A0C29518A0C29518A0C2951890C29518A0C29518A0C29518A0C29518A0C29518A0C29518A0C29518A0C29518A0BC9519A0F3AC2A8EF3A3208EF3BB2AA0C29518A04C4CFC11304EF813344EFD163349FF17314BFC11354DF911394FF813314DF913C29518A055AA"

data = decrypt_data(bytearray.fromhex(hex))

print("decrypted " + data.hex())
