
from datetime import datetime
import glob
import os
import sqlite3
import struct
import zlib

def read2(data,offset):
    return struct.unpack("<H", data[offset:offset+2])[0]

def retrieve_original_package2(packet_buffer: bytes, original_length: int) -> bytes:
    try:
        decompressed_data = zlib.decompress(packet_buffer)
        if len(decompressed_data) < original_length:
            raise ValueError("Decompressed data is shorter than expected original length.")
        return decompressed_data[:original_length]
    except zlib.error as e:
        raise ValueError(f"Decompression failed: {e}")
def retrieve_original_package(packet_buffer: bytes, original_length: int) -> bytes:
    print(packet_buffer.hex())
    original_package = bytearray()
    i = 0
    while i < len(packet_buffer):
        if packet_buffer[i] < 32:
            literal_length = packet_buffer[i] + 1
            i += 1
            for j in range(literal_length):
                if i < len(packet_buffer):
                    original_package.append(packet_buffer[i])
                    i += 1
                else:
                    break
        else:
            if (packet_buffer[i] & 0xE0) == 224:
                repeat_length = (packet_buffer[i] & 0x1F) + 7 + 2 + packet_buffer[i + 1]
                offset = packet_buffer[i + 2] + 1
                i += 3
            else:
                repeat_length = (packet_buffer[i] >> 5) + 2
                offset = packet_buffer[i + 1] + 1
                i += 2

            for j in range(repeat_length):
                if len(original_package) - offset >= 0:
                    original_package.append(original_package[len(original_package) - offset])
                else:
                    raise ValueError("Invalid offset")

    return bytes(original_package[:original_length])

def decompress_packet(packet: bytes) -> bytes:
    original_length = read2(packet, 14)
    compressed_data = packet[16:read2(packet, 12) + 16]
    decompressed_data = retrieve_original_package(compressed_data, original_length)
    return bytearray.fromhex('aa550000') + decompressed_data + bytearray.fromhex('55aa')

def insert_buffer(timestamp, request_type, direction, wordid, sub_packet, buffer,ip,i):
    conn = sqlite3.connect("packets.db")
    cursor = conn.cursor()
    cursor.execute(
        f"""
        INSERT INTO qs (timestamp, src_ip, src_port, dst_ip, dst_port, request_type, direction, raw_data_hex, raw_data_bytes, buffer, wordid)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """,
        (
            timestamp,
            ip,
            1500,
            ip,
            i,
            request_type,
            direction,
            sub_packet.hex(),
            sub_packet,
            buffer,
            wordid,
        ),
    )
    conn.commit()
    last_id = cursor.lastrowid
    conn.close()
    return last_id
def process_file(input_file, output_file,base_name):
    with open(input_file, 'r') as infile, open(output_file, 'w') as outfile:
        i = 0
        for line in infile:
            i+=1
            # Split the line by ':' and strip whitespace
            parts = [part.strip() for part in line.split(':')]
            if len(parts) >= 2:
                key, data = parts[0], parts[1]
                #print(f"Processing line: {data}")
                data = data.replace(' ', '')
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                byte_data = bytearray.fromhex(data)
                request_type = read2(byte_data,8)
                direction = ">>>"
                wordid = read2(byte_data,4)
                insert_buffer(timestamp,  request_type, direction, wordid, byte_data, byte_data,base_name,i)
                outfile.write(f"{key} : {data}\n")

# Example usage

def process_all_files_in_directory(directory_path):
    # List all .txt files in the directory
    txt_files = glob.glob(os.path.join(directory_path, '*.txt'))
    
    for input_file in txt_files:
        # Generate output file name by appending '_out' before the extension
        base_name, extension = os.path.splitext(input_file)
        output_file = f"{base_name}_out{extension}"
        namex = base_name
        # Process each file
        process_file(input_file, output_file,base_name)
        print(f"Processed {input_file} into {output_file}")

# Specify the directory containing your TXT files
directory_path = './input'
process_all_files_in_directory(directory_path)

