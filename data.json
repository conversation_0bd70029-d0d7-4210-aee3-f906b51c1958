{"1,24": {"name": "Search Team", "fn": "SearchTeam"}, "90,1": {"name": "OpenTheHallOfHonor", "fn": "OpenTheHallOfHonor"}, "22,3": {"name": "Top Three Hornors", "fn": "TopThreeHonors"}, "132,0": {"name": "TaskPromtDataSending", "fn": "TaskPromtDataSending"}, "130,0": {"name": "ObtainQuestItems", "fn": "ObtainQuestItems"}, "133,0": {"name": "Update Character Quest", "fn": "UpdateCharacterTask"}, "3,25": {"name": "PropCombinationTips", "fn": "PropCombinationTips"}, "15,28": {"name": "Enter Yuan<PERSON>ao Personal Store", "fn": "EnterYuanbaopersonalStore"}, "10,28": {"name": "PersonalStoreDisplayPackage", "fn": "PersonalStoreDisplayPackage"}, "12,28": {"name": "YuanbaoPersonalStoreFeaturePack", "fn": "YuanbaoPersonalStoreFeaturePack"}, "48,28": {"name": "InquireWhetherThePackageIsOpenedInYuanbaoPersonalStore", "fn": "InquireWhetherThePackageIsOpenedInYuanbaoPersonalStore"}, "196,24": {"name": "YuanbaoPersonalStoreInquiryPackage", "fn": "YuanbaoPersonalStoreInquiryPackage"}, "25,28": {"name": "YuanbaoPersonalStoreInquiryAgreementOpened", "fn": "YuanbaoPersonalStoreInquiryAgreementOpened"}, "22,28": {"name": "YuanbaoPersonalStoreAgreementPackage", "fn": "YuanbaoPersonalStoreAgreementPackage"}, "87,16": {"name": "Name Reminder", "fn": "<PERSON><PERSON><PERSON><PERSON>"}, "85,16": {"name": "Pet Action", "fn": "PetAction"}, "160,0": {"name": "SummonUpdateShowsEquippedItems", "fn": "SummonUpdateShowsEquippedItems"}, "105,0": {"name": "UpdateSpiritBeastHP_MP_SP,Update_HP_MP_SP", "fn": "UpdateSpiritBeastHP_MP_SP,Update_HP_MP_SP"}, "106,0": {"name": "UpdateTheSpiritBeastExperienceAndTrainExperience", "fn": "UpdateTheSpiritBeastExperienceAndTrainExperience"}, "107,0": {"name": "UpdateSpirtiBeastMartialArtsAndStatus", "fn": "UpdateSpirtiBeastMartialArtsAndStatus"}, "124,0": {"name": "UpdateTheWeightOfTheBeast", "fn": "UpdateTheWeightOfTheBeast"}, "113,0": {"name": "UpdateTheEquipmentBasketPackageOfTheSpiritBeastSInitialStory", "fn": "UpdateTheEquipmentBasketPackageOfTheSpiritBeastSInitialStory"}, "224,24": {"name": "SpirtiOrbBag", "fn": "SpiritOrbBag"}, "119,0": {"name": "TipsAfterTheUpgradeOfTheSpiritBeast", "fn": "TipsAfterTheUpgradeOfTheSpiritBeast"}, "120,0": {"name": "ShowPlayers_Party", "fn": "ShowPlayers_Party"}, "205,0": {"name": "Shop_Auto_Treo_Vat_Pham", "fn": "Shop_Auto_Treo_Vat_Pham"}, "208,0": {"name": "IntoTheStore", "fn": "IntoTheStore"}, "116,48": {"name": "OpenChangeCharacter", "fn": "OpenChangeCharacter"}, "6,0": {"name": "Detection", "fn": "Detection"}, "67,1": {"name": "NewStatusEffect", "fn": "NewStatusEffect"}, "176,24": {"name": "HeThong<PERSON><PERSON><PERSON><PERSON><PERSON>New", "fn": "HeThong<PERSON><PERSON><PERSON><PERSON><PERSON>New"}, "47,0": {"name": "Send_Buff_PT,Send_Attack_GroupAttackData2", "fn": "Send_buff_PT"}, "217,24": {"name": "SoulAbsortbingActionPackage", "fn": "SoulAbsorbingActionPackage"}, "35,0": {"name": "SoulAbsorbingQuantityPack", "fn": "SoulAbsorbingQuantityPack"}, "145,0": {"name": "OpenStore", "fn": "OpenStore"}, "213,0": {"name": "BachBao", "fn": "BachBao"}, "150,0": {"name": "OpenPersonalWarehouse", "fn": "OpenPersonalWarehouse"}, "34,0": {"name": "SubtractItems", "fn": "SubtractItems"}, "59,0": {"name": "FourGodsChangePrompt", "fn": "FourGodsChangePrompt"}, "34,1": {"name": "PVPDragonBattle", "fn": "PVPDragonBattle"}, "36,25": {"name": "XemKhiCong", "fn": "XemKhiCong"}, "19,25": {"name": "ViewEquipment", "fn": "ViewEquipment"}, "43,0": {"name": "TeamitemAllocationRules", "fn": "TeamItemAllocationRules"}, "34,25": {"name": "SetDungeonDifficulty", "fn": "SetDungeonDifficulty"}, "10,0": {"name": "SendAttackerData", "fn": "SendAttackerData"}, "118,0": {"name": "UpdateEquipmentEffects", "fn": "UpdateEquipmentEffects"}, "135,0": {"name": "StateEffectCharacterBeast + StatusEffect", "fn": "StateEffectCharacterBeast,StatusEffect"}, "138,0": {"name": "StateEffectsNew", "fn": "StateEffectsNew"}, "126,0": {"name": "SpiritBeastToClass + CharacterToProfession", "fn": "SpiritBeastToClass,CharacterToProfession"}, "127,0": {"name": "EffectOfTakingMedicine", "fn": "EffectOfTakingMedicine"}, "67,16": {"name": "MasterAndApprenticeRequestAcceptance", "fn": "MasterAndApprenticeRequestAcceptance"}, "137,16": {"name": "SelectMonsterPacket", "fn": "SelectMonsterPacket"}, "8,81": {"name": "ThienMaThanCungBieuTuongHienThi", "fn": "ThienMaThanCungBieuTuongHienThi"}, "18,2": {"name": "UpdateMartialArtsCool_old", "fn": "UpdateMartialArtsCool_old"}, "85,1": {"name": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_Du_Guild", "fn": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_Du_Guild"}, "16": {"name": "ChangeDoorService", "fn": "ChangeDoorService"}, "2": {"name": "SwitchPKMode", "fn": "SwitchPkMode"}, "26": {"name": "XacMinhThongTinDangNhapID", "fn": "XacMinhThongTinDangNhapID"}, "14": {"name": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>", "fn": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, "8": {"name": "unkown , DeleteProductionTechnology,DeleteProductionTechnology", "fn": "unkown,DeleteProductionTechnology,DeleteProductionTechnology"}, "5": {"name": "WarehousePharmacy", "fn": "WarehousePharmacy"}, "68": {"name": "CoupleFlowerRequest", "fn": "CoupleFlowerRequest"}, "100": {"name": "PickedItems, UpdateCharacterData", "fn": "PickedItems"}, "62": {"name": "UpdateNpcDatacc", "fn": "UpdateNpcDatacc"}, "21": {"name": "ChangeLineConfirmation", "fn": "ChangeLineConfirmation"}, "132": {"name": "ChangeLineVerification", "fn": "ChangeLineVerification"}, "3477": {"name": "GetCharacterList", "fn": "GetCharacterList"}, "4": {"name": "BackToPeopleList", "fn": "BackToPeopleList"}, "42": {"name": "DangXuat", "fn": "DangXuat"}, "184": {"name": "UnknownFunction1", "fn": "UnknownFunction1"}, "74": {"name": "UnknownFunction4", "fn": "UnknownFunction4"}, "24": {"name": "MoveSwitchingScreen", "fn": "MoveSwitchingScreen"}, "28": {"name": "Mobile,Move", "fn": "Mobile,Move"}, "12": {"name": "GetTheReviewRangePlayers", "fn": "GetTheReviewRangePlayers"}, "104": {"name": "UpdateNPC_DeXoaSoLieu", "fn": "UpdateNPC_DeXoaSoLieu"}, "154": {"name": "CloseCurrentDataWindow", "fn": "CloseCurrentDataWindow"}, "252": {"name": "WaitTimeSync", "fn": "WaitTimeSync"}, "220": {"name": "GetLuckyResult", "fn": "GetLuckyResult"}, "44": {"name": "DangXuat", "fn": "DangXuat"}, "176": {"name": "HearthBeatDetection", "fn": "HearthBeatDetection"}}