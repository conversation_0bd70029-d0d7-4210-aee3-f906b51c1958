
def preprocess_and_decompress(data):
    # L<PERSON>y giá trị eax từ 56E75A0
    eax = int.from_bytes(data[0xA0:0xA4], 'little')
    if eax <= 0:
        print(f"Packet count error: {eax}")
        return None

    # Lấy cx và dx từ 56E75AA và 56E75A8
    cx = int.from_bytes(data[0xAA:0xAC], 'little')
    dx = int.from_bytes(data[0xA8:0xAA], 'little')
    if cx <= 0 or dx <= 0:
        print("Invalid cx or dx values.")
        return None

    # Tăng cx thêm 12
    cx += 12

    # <PERSON><PERSON><PERSON>n dx sang signed integer
    if dx > 0x7FFF:
        dx -= 0x10000

    # L<PERSON>y số byte cần giải nén và địa chỉ dữ liệu
    edi = cx
    start_address = 0xA04C  # G<PERSON><PERSON> đ<PERSON><PERSON> địa chỉ bắt đ<PERSON><PERSON> dữ liệ<PERSON> (từ `esi`)

    # G<PERSON><PERSON> hàm gi<PERSON><PERSON> nén
    decompressed = decompress_data(data[start_address:start_address + dx])
    return decompressed
def decompress_logic(data, input_length, output_length):
    input_index = 0
    output_index = 0
    output = bytearray(output_length)

    while input_index < input_length:
        current_byte = data[input_index]
        input_index += 1

        if current_byte < 32:
            current_byte += 1
            if output_index + current_byte > output_length:
                break

            for _ in range(current_byte):
                output[output_index] = data[input_index]
                output_index += 1
                input_index += 1
        else:
            bit_shift = (current_byte & 0x1F)
            repeat_length = current_byte >> 5

            if repeat_length == 7:
                repeat_length += data[input_index]
                input_index += 1

            copy_start = output_index - bit_shift
            for _ in range(repeat_length):
                if output_index >= output_length or copy_start >= output_length:
                    break

                output[output_index] = output[copy_start]
                output_index += 1
                copy_start += 1

    return bytes(output)

def decompress(data, start_offset, decompressed_size):
    """
    Mô phỏng giải nén từ assembly.
    :param data: Dữ liệu nén (dạng bytes)
    :param start_offset: Địa chỉ bắt đầu của dữ liệu nén (offset)
    :param decompressed_size: Kích thước dữ liệu đã giải nén
    :return: Dữ liệu đã giải nén (dạng bytes)
    """
    # Mô phỏng các thanh ghi
    esi = start_offset   # Dữ liệu nén
    edi = 0              # Con trỏ ghi dữ liệu đã giải nén
    eax = 0
    edx = 0
    ecx = 0

    # Bộ nhớ giải nén
    output = bytearray(decompressed_size)

    while esi < len(data):
        edi = data[esi]
        esi += 1  # Đọc byte đầu tiên

        if edi < 0x20:  # Trường hợp giá trị nhỏ hơn 32
            edi += 1  # Tăng số lượng byte cần sao chép
            if esi + edi > len(data):
                raise ValueError("Không đủ dữ liệu để sao chép.")

            # Sao chép trực tiếp vào output
            output[eax:eax + edi] = data[esi:esi + edi]
            esi += edi
            eax += edi

        else:  # Trường hợp giá trị lớn hơn hoặc bằng 32
            offset = edi & 0x1F   # Offset (5 bit cuối)
            length = edi >> 5     # Độ dài (3 bit đầu)

            if length == 7:  # Nếu length là 7, đọc thêm 1 byte
                if esi >= len(data):
                    raise ValueError("Không đủ dữ liệu để đọc length.")
                length += data[esi]
                esi += 1

            # Tính toán vị trí sao chép
            copy_start = eax - offset
            if copy_start < 0 or copy_start + length > len(output):
                raise ValueError("Sao chép vượt ngoài phạm vi bộ nhớ.")

            # Sao chép từ vùng đã giải nén
            for _ in range(length + 1):
                output[eax] = output[copy_start]
                eax += 1
                copy_start += 1

    return bytes(output)

def decompress_packet(data, offset1, offset2):
    # Giả lập các giá trị được đọc từ bộ nhớ
    value1 = data[offset1]
    value2 = data[offset2]

    if value1 > 240 or value2 > 0x27E:
        print("Packet count error: {}".format(value1))
        return None

    value3 = data[offset1 + 10]
    value4 = data[offset2 + 12]

    if value3 <= 0 or value4 <= 0:
        return None

    value3 += 12
    result = decompress_logic(data, value3, value4)
    return result

compressed_data = bytes.fromhex(
    "64 00 00 00 64 00 C1 01 01 00 00 00 64 20 03 09 54 EA 6E 43 6F EC 44 E2 EC 75 20 0C E0 0F 02 04 01 75 06 04 01 E0 03 1F 0E 01 02 2D B8 DC C5 00 00 70 41 12 5C 16 3F 2D 20 1B 03 CE 48 0D 19 40 21 02 0F A5 07 40 06 01 00 15 A0 07 02 F6 38 0C 60 07 03 3B 95 DA 17 40 08 02 85 1A 06 40 06 E0 08 03 E0 00 15 03 90 81 00 00 E0 03 5C 40 17 00 FF 40 04 E0 03 03 01 FC 21 E0 07 11 E0 07 0F 20 92 E0 04 01 00 02 E0 06 0F E0 17 0E E0 27 2F E0 15 2E 00 FF 20 00 60 50 04 01 A5 CF 9A 3B 60 09 E0 35 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 49 25 00 00 00 00 00 00 00 00 00 00 00"
)

def decompress_data(data):
    input_index = 0
    output = bytearray()

    while input_index < len(data):
        current_byte = data[input_index]
        input_index += 1

        if current_byte < 32:  # Giá trị nhỏ hơn 32
            repeat_count = current_byte + 1
            if input_index + repeat_count > len(data):
                break

            output.extend(data[input_index:input_index + repeat_count])
            input_index += repeat_count
        else:  # Giá trị lớn hơn hoặc bằng 32
            offset = (current_byte & 0x1F)  # Phần offset
            length = (current_byte >> 5)  # Phần length

            if length == 7:  # Nếu length là 7, đọc thêm 1 byte
                if input_index >= len(data):
                    break
                length += data[input_index]
                input_index += 1

            start_index = len(output) - offset
            for _ in range(length):
                if start_index < 0 or start_index >= len(output):
                    break
                output.append(output[start_index])
                start_index += 1

    return bytes(output)


# Thử nghiệm giải nén
decompressed_data = decompress_data(compressed_data)
print("Dữ liệu giải nén:", decompressed_data.hex())