package main

import (
	"encoding/hex"
	"fmt"
)

func main() {
	packetHex := "aa55a9008116c29518a0a4958fa0cd9518a0c29518a0c29518a0c29518a0c29518a0c29518a0c2d77952acb5e8c11cb56cd2374738cca3677180a9fd6d80b468eac3e2f6eccea5b57b5430fb7fa0c29518a0c29518a0c29518a0c29518a0c29518a0c29518a0c29518a0c29518a0c29518a0c29518a0c29518a0c29518a0c29518a0c29518a0c29518a0c29518a0c29518a0c29518a0c29518a0c29518a0c29518a0c29518a0c29518a0c2951855aa"
	packet, err := hex.DecodeString(packetHex)
	if err != nil {
		fmt.Println("Error decoding hex:", err)
		return
	}

	crypto := NewPacketCrypto()
	decrypted, err := crypto.DecryptPacket(packet)
	if err != nil {
		fmt.Println("Error decrypting packet:", err)
		return
	}

	// fmt.Println("Decrypted:", BytesToHex(decrypted))
	fmt.Println("Decrypted:", decrypted)
}
