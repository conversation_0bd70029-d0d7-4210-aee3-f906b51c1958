import json
import pyodbc

# Database connection parameters
server = 'localhost'
database = 'RubyPublicDB'
username = 'sa'
password = '123123'
driver = '{ODBC Driver 17 for SQL Server}'

def import_data(json_path, output_sql_file):
    # Load JSON data
    with open(json_path, 'r') as file:
        json_data = json.load(file)

    # Connect to the SQL Server
    conn = pyodbc.connect(f'DRIVER={driver};SERVER={server};DATABASE={database};UID={username};PWD={password}')
    cursor = conn.cursor()

    # Retrieve all NPC data
    npc_query = "SELECT FLD_PID, FLD_NAME, FLD_HP, FLD_AT, FLD_DF, FLD_NPC,  FLD_LEVEL, FLD_EXP, FLD_AUTO, FLD_BOSS, FLD_FreeDrop FROM dbo.TBL_XWWL_MONSTER"
    cursor.execute(npc_query)
    npc_data = {row.FLD_PID: row for row in cursor.fetchall()}

    # Open output file for writing SQL queries
    with open(output_sql_file, 'w', encoding='utf-8') as f:
        for item in json_data:
            mid = int(item["1"])
            pid = int(item["2"])
            x = float(item["3"])
            y = float(item["4"])
            z = 15.0  # Static value as specified
            amount = 20
            aoe = 50
            if (pid < 10000):
                amount = 1
                aoe = 0
            npc = npc_data.get(pid)

            if npc:
                values = (pid, x, z, y, 0, 0, mid, npc.FLD_NAME, npc.FLD_HP, npc.FLD_AT, npc.FLD_DF, npc.FLD_NPC,
                          5, npc.FLD_LEVEL, npc.FLD_EXP, npc.FLD_AUTO, npc.FLD_BOSS, amount, aoe,
                          0, 0, 0, 0, npc.FLD_FreeDrop)
                
                # Construct the INSERT statement with actual values
                insert_query = f"INSERT INTO dbo.TBL_XWWL_MONSTER_SET_BASE (FLD_PID, FLD_X, FLD_Z, FLD_Y, FLD_FACE0, FLD_FACE, FLD_MID, FLD_NAME, FLD_HP, FLD_AT, FLD_DF, FLD_NPC, FLD_NEWTIME, FLD_LEVEL, FLD_EXP, FLD_AUTO, FLD_BOSS, FLD_AMOUNT, FLD_AOE, FLD_Accuracy, FLD_Evasion, FLD_QItemDrop, FLD_QDropPP, FLD_FreeDrop) VALUES ({', '.join(['?' for _ in values])});\n"
                formatted_query = insert_query.replace('?', '{}').format(*[repr(v) if isinstance(v, str) else v for v in values])
                f.write(formatted_query)

    print("SQL queries have been written to", output_sql_file)
    cursor.close()
    conn.close()

json_path = 'monstersetbase.json'
output_sql_file = 'insert_commands.sql'
import_data(json_path, output_sql_file)
