#!/usr/bin/env python3
from scapy.all import *
import time
import datetime

def packet_callback(packet):
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    
    if TCP in packet:
        src_ip = packet[IP].src
        dst_ip = packet[IP].dst
        src_port = packet[TCP].sport
        dst_port = packet[TCP].dport
        
        # Chỉ xử lý các packet đến localhost:13000
        if dst_ip == '127.0.0.1' and dst_port == 13000:
            # Xác định các packet "khác thường"
            is_unusual = False
            reason = ""

            # Kiểm tra cờ TCP
            flags = packet[TCP].flags
            if 'R' in flags:  # RST (Reset)
                is_unusual = True
                reason = "RST flag detected"
            elif 'U' in flags:  # URG (Urgent)
                is_unusual = True
                reason = "URG flag detected"
            elif not (flags & 0x10 or flags & 0x18):  # Không có ACK hoặc PSH+ACK
                if not ('S' in flags or 'F' in flags):  # Không phải SYN hoặc FIN
                    is_unusual = True
                    reason = f"Unusual flags: {flags}"

            # Kiểm tra dữ liệu rỗng hoặc kích thước bất thường
            if Raw in packet:
                data_len = len(packet[Raw].load)
                if data_len == 0:  # Packet có Raw nhưng không có dữ liệu
                    is_unusual = True
                    reason = "Empty Raw data"
                elif data_len > 1500:  # Packet quá lớn (tùy chỉnh ngưỡng)
                    is_unusual = True
                    reason = f"Unusually large data: {data_len} bytes"
            else:
                # Packet không có dữ liệu Raw (có thể là packet kiểm soát)
                if not ('S' in flags or 'F' in flags or 'A' in flags):
                    is_unusual = True
                    reason = "No Raw data and unusual flags"

            # In ra nếu packet được coi là "khác thường"
            if is_unusual:
                print(f"[{timestamp}] {src_ip}:{src_port} ==> {dst_ip}:{dst_port}")
                if Raw in packet:
                    print(f"    Dữ liệu: {packet[Raw].load}")
                print(f"    Cờ: {flags}")
                print(f"    Kích thước: {len(packet)} bytes")
                print(f"    Lý do: {reason}")
                print("-" * 80)

def main():
    print("Bắt đầu bắt gói tin khác thường đến localhost:13000...")
    print("Nhấn Ctrl+C để dừng.")
    try:
        # Bắt gói tin đến localhost cổng 13000
        loopback_iface = '\\Device\\NPF_Loopback'
        sniff(filter="tcp and dst host 127.0.0.1 and dst port 13000", iface=loopback_iface, prn=packet_callback, store=0)
    except KeyboardInterrupt:
        print("\nĐã dừng bắt gói tin.")

if __name__ == "__main__":
    main()