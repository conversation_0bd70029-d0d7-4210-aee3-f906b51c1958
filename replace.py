def modify_sql_inserts(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    with open(output_file, 'w', encoding='utf-8') as f:
        for index, line in enumerate(lines):
            # Generate the new product code with the line number (1-based index), now enclosed in quotes
            new_product_code = f"'PRODUCT{index + 1:04d}'"
            # Replace 'PRODUCT' with 'new_product_code' in the SQL statement
            # Ensuring 'PRODUCT' appears exactly as expected, possibly needing to adjust how it's identified if it varies
            modified_line = line.replace("PRODUCT", new_product_code)
            f.write(modified_line)

# Example usage:
input_file = 'input.sql'  # This file should contain your SQL insert statements
output_file = 'modified_output.sql'
modify_sql_inserts(input_file, output_file)
