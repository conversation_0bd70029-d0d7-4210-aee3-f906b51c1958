def process_data(data):
    eax = 0
    esi = 0
    edx = len(data)
    edi = 0
    ecx = 0
    ebx = 0
    output = bytearray()

    while eax < edx:
        if esi > 499:
            print("Breaking because esi > 499:", esi)
            break

        edi = data[esi]
        ecx = esi
        esi += 1

        # Kiểm tra điều kiện đầu tiên
        if edi >= 0x20:
            edi += 1
            ecx = edi + eax
            if ecx >= edx:
                print("Jump to 6AEB3A because ecx >= edx")
                ebx = edi & 0x1F
                edi <<= 8
                ebx >>= 5
                edx -= edi
                edx -= 1

                if ebx == 7:
                    ebx = data[esi]
                    esi += 1
                    ebx += 7

                ecx = data[esi]
                esi += 1
                edx -= ecx

                if edx <= 0:
                    print("Jump to 6AEB3A because edx <= 0 after processing")
                    return output

            else:
                while edi > 0:
                    if esi >= len(data):
                        print("Breaking because esi >= len(data) during copy loop")
                        break
                    output.append(data[esi])
                    esi += 1
                    edi -= 1

        else:
            edi += 1
            ecx = edi + eax
            if ecx >= edx:
                print("Jump to 6AEB3A because ecx >= edx in else branch")
                return output

            while edi > 0:
                if esi >= len(data):
                    print("Breaking because esi >= len(data) during copy loop in else branch")
                    break
                output.append(data[esi])
                esi += 1
                edi -= 1

    return output
# Ví dụ sử dụng hàm
input_data = bytearray.fromhex("0C 64 00 00 00 64 00 C1 01 01 00 00 00 64 20 03 09 54 EA 6E 43 6F EC 44 E2 EC 75 20 0C E0 0F 02 04 01 75 06 04 01 E0 03 1F 0E 01 02 30 5A DD C5 00 00 70 41 BF 9A CE 40 2D 20 1B 03 CE 48 0D 19 40 21 02 0F A5 07 40 06 01 00 15 A0 07 02 F6 38 0C 60 07 03 3B 95 DA 17 40 08 02 85 1A 06 40 06 E0 08 03 E0 00 15 03 90 81 00 00 E0 03 5C 40 17 00 FF 40 04 E0 03 03 01 04 24 E0 07 11 E0 07 0F 20 92 E0 04 01 00 02 E0 06 0F E0 17 0E E0 27 2F E0 15 2E 00 FF 20 00 60 50 04 01 A5 CF 9A 3B 60 09 E0 35 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 16 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00".replace(" ",""));
output_data = process_data(input_data)
print("Processed Data:", output_data.hex())