#!/usr/bin/env python3
import tkinter as tk
from tkinter import ttk, scrolledtext, filedialog, messagebox
import socket
import json
import threading
import os
import sys
import signal
import time
import subprocess

class PacketGUI:
    def __init__(self, root, socket_path=None):
        self.root = root
        self.socket_path = socket_path
        self.socket = None
        self.connected = False
        self.running = False
        self.capture_process = None
        
        # Thiết lập giao diện
        self.setup_ui()
        
        # Kết nối đến socket
        if self.socket_path:
            self.connect_thread = threading.Thread(target=self.connect_to_capture)
            self.connect_thread.daemon = True
            self.connect_thread.start()
    
    def setup_ui(self):
        self.root.title("Packet Analyzer")
        self.root.geometry("800x600")
        
        # Frame chính
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Frame đ<PERSON><PERSON>u <PERSON>
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)
        
        # Nút Start/Stop
        self.start_button = ttk.Button(control_frame, text="Start Capture", command=self.toggle_capture)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        # Nút Clear
        self.clear_button = ttk.Button(control_frame, text="Clear", command=self.clear_display)
        self.clear_button.pack(side=tk.LEFT, padx=5)
        
        # Nút Save
        self.save_button = ttk.Button(control_frame, text="Save", command=self.save_packets)
        self.save_button.pack(side=tk.LEFT, padx=5)
        
        # Trạng thái kết nối
        self.status_label = ttk.Label(control_frame, text="Disconnected")
        self.status_label.pack(side=tk.RIGHT, padx=5)
        
        # Frame hiển thị gói tin
        packet_frame = ttk.Frame(main_frame)
        packet_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Danh sách gói tin
        columns = ('Time', 'Source', 'Destination', 'Protocol', 'Length')
        self.packet_tree = ttk.Treeview(packet_frame, columns=columns, show='headings')
        
        # Thiết lập tiêu đề cột
        for col in columns:
            self.packet_tree.heading(col, text=col)
            self.packet_tree.column(col, width=100)
        
        # Thanh cuộn
        scrollbar = ttk.Scrollbar(packet_frame, orient=tk.VERTICAL, command=self.packet_tree.yview)
        self.packet_tree.configure(yscroll=scrollbar.set)
        
        # Đặt vị trí
        self.packet_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Khung hiển thị chi tiết gói tin
        details_frame = ttk.LabelFrame(main_frame, text="Packet Details")
        details_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.details_text = scrolledtext.ScrolledText(details_frame, wrap=tk.WORD)
        self.details_text.pack(fill=tk.BOTH, expand=True)
        
        # Xử lý sự kiện khi chọn gói tin
        self.packet_tree.bind('<<TreeviewSelect>>', self.on_packet_select)
        
        # Lưu trữ dữ liệu gói tin
        self.packets = []
    
    def connect_to_capture(self):
        while not self.connected and self.root.winfo_exists():
            try:
                if not os.path.exists(self.socket_path):
                    time.sleep(1)
                    continue
                
                self.socket = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
                self.socket.connect(self.socket_path)
                self.socket.settimeout(0.1)  # Non-blocking socket
                
                self.connected = True
                self.root.after(0, lambda: self.status_label.config(text="Connected"))
                
                # Bắt đầu thread đọc dữ liệu
                self.read_thread = threading.Thread(target=self.read_packets)
                self.read_thread.daemon = True
                self.read_thread.start()
                
            except (socket.error, FileNotFoundError):
                time.sleep(1)
    
    def read_packets(self):
        buffer = ""
        while self.connected and self.root.winfo_exists():
            try:
                data = self.socket.recv(4096).decode('utf-8')
                if not data:
                    self.disconnect()
                    break
                
                buffer += data
                
                # Xử lý từng dòng JSON
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    try:
                        packet = json.loads(line)
                        self.packets.append(packet)
                        
                        # Cập nhật UI trong main thread
                        self.root.after(0, lambda p=packet: self.add_packet_to_ui(p))
                    except json.JSONDecodeError:
                        pass
                
            except socket.timeout:
                continue
            except Exception as e:
                print(f"Lỗi khi đọc gói tin: {e}")
                self.disconnect()
                break
    
    def add_packet_to_ui(self, packet):
        # Thêm gói tin vào treeview
        item_id = self.packet_tree.insert('', 'end', values=(
            packet['timestamp'],
            packet['source_ip'],
            packet['dest_ip'],
            packet['protocol'],
            len(packet['data']) // 2  # Độ dài dữ liệu hex
        ))
        
        # Tự động cuộn đến mục mới nhất
        self.packet_tree.see(item_id)
    
    def on_packet_select(self, event):
        selected_items = self.packet_tree.selection()
        if selected_items:
            item_id = selected_items[0]
            item_index = self.packet_tree.index(item_id)
            
            if 0 <= item_index < len(self.packets):
                packet = self.packets[item_index]
                
                # Hiển thị chi tiết gói tin
                self.details_text.delete(1.0, tk.END)
                
                # Định dạng hiển thị
                details = f"Timestamp: {packet['timestamp']}\n"
                details += f"Source IP: {packet['source_ip']}\n"
                details += f"Destination IP: {packet['dest_ip']}\n"
                details += f"Protocol: {packet['protocol']}\n\n"
                
                # Hiển thị dữ liệu hex
                hex_data = packet['data']
                details += "Hex Data:\n"
                
                # Định dạng hex dump
                for i in range(0, len(hex_data), 32):
                    line = hex_data[i:i+32]
                    hex_part = ' '.join(line[j:j+2] for j in range(0, len(line), 2))
                    
                    # Chuyển đổi hex thành ASCII
                    ascii_part = ''
                    for j in range(0, len(line), 2):
                        if j+1 < len(line):
                            try:
                                byte = int(line[j:j+2], 16)
                                ascii_part += chr(byte) if 32 <= byte <= 126 else '.'
                            except:
                                ascii_part += '.'
                    
                    details += f"{i//2:08x}:  {hex_part.ljust(48)}  {ascii_part}\n"
                
                self.details_text.insert(tk.END, details)
    
    def toggle_capture(self):
        if not self.running:
            # Bắt đầu quá trình bắt gói tin
            self.start_capture()
            self.start_button.config(text="Stop Capture")
            self.running = True
        else:
            # Dừng quá trình bắt gói tin
            self.stop_capture()
            self.start_button.config(text="Start Capture")
            self.running = False
    
    def start_capture(self):
        if self.capture_process is None:
            try:
                # Khởi động quá trình bắt gói tin với sudo
                cmd = ['sudo', 'python3', 'packet_capture.py', '-s', self.socket_path]
                self.capture_process = subprocess.Popen(cmd)
                
                # Cập nhật trạng thái
                self.status_label.config(text="Starting capture...")
            except Exception as e:
                messagebox.showerror("Error", f"Không thể khởi động quá trình bắt gói tin: {e}")
    
    def stop_capture(self):
        if self.capture_process:
            try:
                self.capture_process.terminate()
                self.capture_process = None
            except:
                pass
        
        self.disconnect()
    
    def disconnect(self):
        self.connected = False
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        
        self.status_label.config(text="Disconnected")
    
    def clear_display(self):
        # Xóa tất cả gói tin
        for item in self.packet_tree.get_children():
            self.packet_tree.delete(item)
        
        self.details_text.delete(1.0, tk.END)
        self.packets = []
    
    def save_packets(self):
        if not self.packets:
            messagebox.showinfo("Info", "Không có gói tin để lưu")
            return
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w') as f:
                    json.dump(self.packets, f, indent=2)
                messagebox.showinfo("Success", f"Đã lưu {len(self.packets)} gói tin vào {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Lỗi khi lưu file: {e}")

def signal_handler(sig, frame):
    if 'root' in globals() and root.winfo_exists():
        root.quit()
    sys.exit(0)

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Giao diện phân tích gói tin')
    parser.add_argument('-s', '--socket', default='/tmp/packet_capture.sock', 
                        help='Đường dẫn đến socket Unix để giao tiếp với quá trình bắt gói tin')
    
    args = parser.parse_args()
    
    # Đăng ký xử lý tín hiệu
    signal.signal(signal.SIGINT, signal_handler)
    
    # Tạo giao diện
    root = tk.Tk()
    app = PacketGUI(root, socket_path=args.socket)
    root.protocol("WM_DELETE_WINDOW", lambda: (app.stop_capture(), root.destroy()))
    root.mainloop()