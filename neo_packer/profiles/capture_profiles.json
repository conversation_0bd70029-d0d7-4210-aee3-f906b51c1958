[{"ID": 1, "Name": "HTTP/HTTPS", "Description": "Capture HTTP and HTTPS traffic", "IP": "", "Ports": [80, 443], "PortRanges": [], "IsActive": false}, {"ID": 2, "Name": "Database", "Description": "Capture common database ports", "IP": "", "Ports": [1433, 3306, 5432, 27017], "PortRanges": [], "IsActive": false}, {"ID": 3, "Name": "Dynamic Ports", "Description": "Capture traffic on dynamic/private ports", "IP": "", "Ports": [], "PortRanges": [{"Start": 49152, "End": 65535}], "IsActive": false}, {"ID": 4, "Name": "yg", "Description": "yg", "IP": "127.0.0.1", "Ports": [9000, 9001], "PortRanges": [], "IsActive": false}]