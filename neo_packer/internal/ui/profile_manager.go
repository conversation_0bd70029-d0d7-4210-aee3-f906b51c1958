package ui

import (
	"fmt"
	"strconv"
	"strings"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/layout"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"

	"1l4e/neo_packer/internal/models"
)

// showProfileManager displays the profile management dialog
func (u *UI) showProfileManager() {
	// Create a window for profile management
	w := u.app.NewWindow("Capture Profile Manager")
	w.Resize(fyne.NewSize(800, 600))

	// Create a list to display existing profiles
	profileList := widget.NewList(
		func() int { return len(u.profiles) },
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewLabel("Profile Name"),
				widget.NewLabel("IP"),
				widget.<PERSON>Label("Ports"),
				widget.<PERSON>Label("Status"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id >= len(u.profiles) {
				return
			}

			profile := u.profiles[id]
			container := obj.(*fyne.Container)

			// Profile name
			container.Objects[0].(*widget.Label).SetText(profile.Name)

			// IP
			container.Objects[1].(*widget.Label).SetText(profile.IP)

			// Ports
			var portStr string
			if len(profile.Ports) > 0 {
				ports := make([]string, len(profile.Ports))
				for i, port := range profile.Ports {
					ports[i] = strconv.Itoa(port)
				}
				portStr = strings.Join(ports, ", ")
			}

			if len(profile.PortRanges) > 0 {
				ranges := make([]string, len(profile.PortRanges))
				for i, r := range profile.PortRanges {
					ranges[i] = fmt.Sprintf("%d-%d", r.Start, r.End)
				}
				if portStr != "" {
					portStr += ", "
				}
				portStr += strings.Join(ranges, ", ")
			}

			container.Objects[2].(*widget.Label).SetText(portStr)

			// Status
			status := "Inactive"
			if profile.IsActive {
				status = "Active"
			}
			container.Objects[3].(*widget.Label).SetText(status)
		},
	)

	// Create form for adding new profiles
	nameEntry := widget.NewEntry()
	nameEntry.SetPlaceHolder("Profile Name")

	descEntry := widget.NewMultiLineEntry()
	descEntry.SetPlaceHolder("Description")

	ipEntry := widget.NewEntry()
	ipEntry.SetPlaceHolder("IP Address (optional)")

	portsEntry := widget.NewEntry()
	portsEntry.SetPlaceHolder("Ports (comma separated, e.g. 80,443)")

	portRangesEntry := widget.NewEntry()
	portRangesEntry.SetPlaceHolder("Port Ranges (e.g. 1000-2000,3000-4000)")

	// Add profile button
	addButton := widget.NewButtonWithIcon("Add Profile", theme.ContentAddIcon(), func() {
		// Validate inputs
		if nameEntry.Text == "" {
			dialog.ShowError(fmt.Errorf("Profile name is required"), w)
			return
		}

		// Create new profile
		profile := models.CaptureProfile{
			ID:          int64(len(u.profiles) + 1),
			Name:        nameEntry.Text,
			Description: descEntry.Text,
			IP:          ipEntry.Text,
			Ports:       []int{},
			PortRanges:  []models.PortRange{},
			IsActive:    false,
		}

		// Parse ports
		if portsEntry.Text != "" {
			for _, portStr := range strings.Split(portsEntry.Text, ",") {
				portStr = strings.TrimSpace(portStr)
				if portStr == "" {
					continue
				}

				port, err := strconv.Atoi(portStr)
				if err != nil {
					dialog.ShowError(fmt.Errorf("Invalid port: %s", portStr), w)
					return
				}

				profile.Ports = append(profile.Ports, port)
			}
		}

		// Parse port ranges
		if portRangesEntry.Text != "" {
			for _, rangeStr := range strings.Split(portRangesEntry.Text, ",") {
				rangeStr = strings.TrimSpace(rangeStr)
				if rangeStr == "" {
					continue
				}

				parts := strings.Split(rangeStr, "-")
				if len(parts) != 2 {
					dialog.ShowError(fmt.Errorf("Invalid port range: %s", rangeStr), w)
					return
				}

				start, err := strconv.Atoi(strings.TrimSpace(parts[0]))
				if err != nil {
					dialog.ShowError(fmt.Errorf("Invalid port range start: %s", parts[0]), w)
					return
				}

				end, err := strconv.Atoi(strings.TrimSpace(parts[1]))
				if err != nil {
					dialog.ShowError(fmt.Errorf("Invalid port range end: %s", parts[1]), w)
					return
				}

				if start > end {
					dialog.ShowError(fmt.Errorf("Port range start must be less than or equal to end"), w)
					return
				}

				profile.PortRanges = append(profile.PortRanges, models.PortRange{
					Start: start,
					End:   end,
				})
			}
		}

		// Add profile to list
		u.profiles = append(u.profiles, profile)
		profileList.Refresh()

		// Update profile select in main UI
		u.updateProfileSelect()

		// Clear form
		nameEntry.SetText("")
		descEntry.SetText("")
		ipEntry.SetText("")
		portsEntry.SetText("")
		portRangesEntry.SetText("")
	})

	// Add OnSelected handler to profileList
	var selectedProfileIndex int = -1
	profileList.OnSelected = func(id widget.ListItemID) {
		selectedProfileIndex = id
	}

	// Delete profile button
	deleteButton := widget.NewButtonWithIcon("Delete Profile", theme.DeleteIcon(), func() {
		if selectedProfileIndex < 0 || selectedProfileIndex >= len(u.profiles) {
			dialog.ShowError(fmt.Errorf("No profile selected"), w)
			return
		}

		// Confirm deletion
		dialog.ShowConfirm("Delete Profile",
			fmt.Sprintf("Are you sure you want to delete profile '%s'?", u.profiles[selectedProfileIndex].Name),
			func(confirm bool) {
				if !confirm {
					return
				}

				// Remove profile
				u.profiles = append(u.profiles[:selectedProfileIndex], u.profiles[selectedProfileIndex+1:]...)
				profileList.Refresh()
				u.updateProfileSelect()
			}, w)
	})

	// Save profiles button
	saveButton := widget.NewButtonWithIcon("Save Profiles", theme.DocumentSaveIcon(), func() {
		u.saveProfiles()
	})

	// Close button
	closeButton := widget.NewButtonWithIcon("Close", theme.CancelIcon(), func() {
		w.Close()
	})

	// Layout
	form := container.NewVBox(
		widget.NewLabel("Add New Profile"),
		container.NewGridWithColumns(2,
			widget.NewLabel("Name:"),
			nameEntry,
		),
		container.NewGridWithColumns(2,
			widget.NewLabel("Description:"),
			descEntry,
		),
		container.NewGridWithColumns(2,
			widget.NewLabel("IP Address:"),
			ipEntry,
		),
		container.NewGridWithColumns(2,
			widget.NewLabel("Ports:"),
			portsEntry,
		),
		container.NewGridWithColumns(2,
			widget.NewLabel("Port Ranges:"),
			portRangesEntry,
		),
		addButton,
	)

	buttons := container.NewHBox(
		layout.NewSpacer(),
		deleteButton,
		saveButton,
		closeButton,
	)

	content := container.NewBorder(
		container.NewVBox(
			widget.NewLabel("Capture Profiles"),
			widget.NewSeparator(),
		),
		buttons,
		nil,
		nil,
		container.NewHSplit(
			container.NewScroll(profileList),
			container.NewScroll(form),
		),
	)

	w.SetContent(content)
	w.Show()
}

// updateProfileSelect updates the profile selection dropdown in the main UI
func (u *UI) updateProfileSelect() {
	var names []string
	for _, profile := range u.profiles {
		names = append(names, profile.Name)
	}

	u.profileSelect.Options = names
	if len(names) > 0 && u.profileSelect.Selected == "" {
		u.profileSelect.SetSelected(names[0])
	}
	u.profileSelect.Refresh()
}
