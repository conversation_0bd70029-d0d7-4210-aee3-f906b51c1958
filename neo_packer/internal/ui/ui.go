package ui

import (
	"encoding/binary"
	"fmt"
	"image/color"
	"strconv"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/layout"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"

	"1l4e/neo_packer/internal/capture"
	"1l4e/neo_packer/internal/models"
	"1l4e/neo_packer/internal/parser"
	"1l4e/neo_packer/internal/utils"
)

// myTheme là một theme tùy chỉnh cho phép chỉ định variant (dark/light)
type myTheme struct {
	variant fyne.ThemeVariant
}

// Đảm bảo myTheme triển khai giao diện fyne.Theme
var _ fyne.Theme = (*myTheme)(nil)

// Color trả về màu cho tên màu và variant được chỉ định
func (m *myTheme) Color(name fyne.ThemeColorName, variant fyne.ThemeVariant) color.Color {
	// Sử dụng variant được chỉ định thay vì tham số variant
	return theme.DefaultTheme().Color(name, m.variant)
}

// Font trả về font cho kiểu văn bản
func (m *myTheme) Font(style fyne.TextStyle) fyne.Resource {
	return theme.DefaultTheme().Font(style)
}

// Icon trả về biểu tượng cho tên biểu tượng
func (m *myTheme) Icon(name fyne.ThemeIconName) fyne.Resource {
	return theme.DefaultTheme().Icon(name)
}

// Size trả về kích thước cho tên kích thước
func (m *myTheme) Size(name fyne.ThemeSizeName) float32 {
	return theme.DefaultTheme().Size(name)
}

// UI represents the user interface
type UI struct {
	app             fyne.App
	mainWindow      fyne.Window
	capturer        *capture.Capturer
	parser          *parser.Parser
	interfaces      []models.NetworkInterface
	servers         []models.Server
	packets         []models.Packet
	selectedPacket  *models.Packet
	isCapturing     bool
	profiles        []models.CaptureProfile
	selectedProfile *models.CaptureProfile
	decryptPackets  bool // Trạng thái decrypt packet

	// UI components
	interfaceSelect    *widget.Select
	serverSelect       *widget.Select
	profileSelect      *widget.Select
	startButton        *widget.Button
	stopButton         *widget.Button
	packetList         *widget.List
	hexView            *widget.RichText
	dataInterpretation *widget.Label
	statusBar          *widget.Label
	filterEntry        *widget.Entry
	typeSelect         *widget.Select
	darkModeToggle     *widget.Check
	decryptToggle      *widget.Check // Checkbox để bật/tắt decrypt

	// Profile management components
	profileList *widget.List
}

// NewUI creates a new UI instance
func NewUI() *UI {
	ui := &UI{
		app:         app.New(),
		parser:      parser.NewParser(),
		interfaces:  []models.NetworkInterface{},
		servers:     []models.Server{},
		packets:     []models.Packet{},
		profiles:    []models.CaptureProfile{},
		isCapturing: false,
	}

	ui.mainWindow = ui.app.NewWindow("Neo Packer")
	ui.mainWindow.Resize(fyne.NewSize(1200, 800))

	// Initialize UI components
	ui.createUI()

	// Load interfaces
	ui.loadInterfaces()

	// Load servers (placeholder)
	ui.loadServers()

	// Load capture profiles
	ui.loadProfiles()

	return ui
}

// Run starts the UI
func (u *UI) Run() {
	u.mainWindow.ShowAndRun()
}

// createUI builds the user interface
func (u *UI) createUI() {
	// Top toolbar
	u.interfaceSelect = widget.NewSelect([]string{}, func(value string) {})
	u.serverSelect = widget.NewSelect([]string{}, func(value string) {})
	u.profileSelect = widget.NewSelect([]string{}, func(value string) {
		// Update selected profile when changed
		for i, profile := range u.profiles {
			if profile.Name == value {
				u.selectedProfile = &u.profiles[i]
				break
			}
		}
	})

	// Profile management button
	profileManagerButton := widget.NewButtonWithIcon("Manage Profiles", theme.ListIcon(), u.showProfileManager)

	u.startButton = widget.NewButtonWithIcon("Start Capture", theme.MediaPlayIcon(), u.startCapture)
	u.stopButton = widget.NewButtonWithIcon("Stop Capture", theme.MediaStopIcon(), u.stopCapture)
	u.stopButton.Disable()

	// Create tabs for different capture modes
	captureModeTabs := container.NewAppTabs(
		container.NewTabItem("Server Mode", container.NewHBox(
			widget.NewLabel("Server:"),
			u.serverSelect,
		)),
		container.NewTabItem("Profile Mode", container.NewHBox(
			widget.NewLabel("Profile:"),
			u.profileSelect,
			profileManagerButton,
		)),
	)

	toolbar := container.NewVBox(
		container.NewHBox(
			widget.NewLabel("Interface:"),
			u.interfaceSelect,
			layout.NewSpacer(),
			u.startButton,
			u.stopButton,
		),
		captureModeTabs,
	)

	// Filter bar
	u.filterEntry = widget.NewEntry()
	u.filterEntry.SetPlaceHolder("Filter by type (e.g. 103,642)")
	filterButton := widget.NewButtonWithIcon("Filter", theme.SearchIcon(), u.applyFilter)

	u.typeSelect = widget.NewSelect([]string{"All"}, func(value string) {
		u.filterByType(value)
	})

	filterBar := container.NewHBox(
		widget.NewLabel("Type:"),
		u.typeSelect,
		u.filterEntry,
		filterButton,
	)

	// Packet list
	u.packetList = widget.NewList(
		func() int { return len(u.packets) },
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewLabel("00000"),
				widget.NewLabel("00:00:00"),
				widget.NewLabel("***************"),
				widget.NewLabel("00000"),
				widget.NewLabel("0000"),
				widget.NewLabel(">>>"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id >= len(u.packets) {
				return
			}

			packet := u.packets[id]
			container := obj.(*fyne.Container)

			// ID
			container.Objects[0].(*widget.Label).SetText(fmt.Sprintf("%d", id))

			// Time
			container.Objects[1].(*widget.Label).SetText(packet.Timestamp.Format("15:04:05.000"))

			// IP
			container.Objects[2].(*widget.Label).SetText(packet.DstIP)

			// Port
			container.Objects[3].(*widget.Label).SetText(fmt.Sprintf("%d", packet.DstPort))

			// Type
			container.Objects[4].(*widget.Label).SetText(fmt.Sprintf("%d", packet.RequestType))

			// Direction
			container.Objects[5].(*widget.Label).SetText(packet.Direction)

			// Set color based on direction
			if packet.Direction == ">>>" {
				for i := range container.Objects {
					container.Objects[i].(*widget.Label).TextStyle = fyne.TextStyle{Bold: true}
				}
			} else {
				for i := range container.Objects {
					container.Objects[i].(*widget.Label).TextStyle = fyne.TextStyle{}
				}
			}
		},
	)

	u.packetList.OnSelected = u.onPacketSelected

	// Hex view
	u.hexView = widget.NewRichText()
	hexScroll := container.NewScroll(u.hexView)

	// Data interpretation
	u.dataInterpretation = widget.NewLabel("")

	// Status bar
	u.statusBar = widget.NewLabel("Ready")

	// Dark mode toggle
	u.darkModeToggle = widget.NewCheck("Dark Mode", func(checked bool) {
		// Tạo theme tùy chỉnh với variant được chỉ định
		customTheme := &myTheme{variant: theme.VariantLight}
		if checked {
			customTheme.variant = theme.VariantDark
		}
		u.app.Settings().SetTheme(customTheme)
	})

	// Decrypt toggle
	u.decryptToggle = widget.NewCheck("Decrypt Packets", func(checked bool) {
		u.decryptPackets = checked
		// Nếu có packet đã chọn, hiển thị lại với trạng thái decrypt mới
		if u.selectedPacket != nil {
			u.displayPacketDetails(*u.selectedPacket)
		}
		// Refresh danh sách packet để cập nhật hiển thị
		u.packetList.Refresh()
	})

	// Layout
	leftPanel := container.NewBorder(
		filterBar,
		nil,
		nil,
		nil,
		container.NewScroll(u.packetList),
	)

	rightPanel := container.NewBorder(
		nil,
		container.NewVBox(
			widget.NewSeparator(),
			container.NewHBox(
				widget.NewLabel("Data Interpretation:"),
			),
			container.NewVScroll(u.dataInterpretation),
		),
		nil,
		nil,
		hexScroll,
	)

	// Main split
	split := container.NewHSplit(
		leftPanel,
		rightPanel,
	)
	split.Offset = 0.3

	// Main layout
	content := container.NewBorder(
		container.NewVBox(
			toolbar,
			widget.NewSeparator(),
		),
		container.NewHBox(
			u.statusBar,
			layout.NewSpacer(),
			u.decryptToggle,
			u.darkModeToggle,
		),
		nil,
		nil,
		split,
	)

	u.mainWindow.SetContent(content)
}

// loadInterfaces loads available network interfaces
func (u *UI) loadInterfaces() {
	interfaces, err := capture.GetInterfaces()
	if err != nil {
		dialog.ShowError(fmt.Errorf("Failed to load interfaces: %v", err), u.mainWindow)
		return
	}

	u.interfaces = interfaces

	var names []string
	for _, iface := range interfaces {
		desc := iface.Description
		if desc == "" {
			desc = iface.Name
		}
		names = append(names, fmt.Sprintf("%s (%s)", iface.Name, desc))
	}

	u.interfaceSelect.Options = names
	if len(names) > 0 {
		u.interfaceSelect.SetSelected(names[0])
	}
}

// loadServers loads server configurations
func (u *UI) loadServers() {
	// Placeholder - in a real app, this would load from config
	u.servers = []models.Server{
		{
			ID:          1,
			Name:        "Local Server",
			IP:          "127.0.0.1",
			TargetPort:  8000,
			LoginPort:   8001,
			IsLocal:     true,
			IsEncrypted: false,
		},
	}

	var names []string
	for _, server := range u.servers {
		names = append(names, server.Name)
	}

	u.serverSelect.Options = names
	if len(names) > 0 {
		u.serverSelect.SetSelected(names[0])
	}
}

// startCapture begins packet capture
func (u *UI) startCapture() {
	if u.isCapturing {
		return
	}

	// Get selected interface
	ifaceIdx := u.interfaceSelect.SelectedIndex()
	if ifaceIdx < 0 || ifaceIdx >= len(u.interfaces) {
		dialog.ShowError(fmt.Errorf("Please select a network interface"), u.mainWindow)
		return
	}

	// Create output file
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("capture_%s.pcap", timestamp)

	// Determine capture mode (server or profile)
	usingProfile := u.selectedProfile != nil && u.profileSelect.Selected != ""

	// Create capturer based on selected mode
	if usingProfile {
		// Create capturer with profile
		u.capturer = capture.NewCapturerWithProfile(
			*u.selectedProfile,
			u.interfaces[ifaceIdx].Name,
			u.onPacketCaptured,
		)
	} else {
		// Get selected server
		serverIdx := u.serverSelect.SelectedIndex()
		if serverIdx < 0 || serverIdx >= len(u.servers) {
			dialog.ShowError(fmt.Errorf("Please select a server or profile"), u.mainWindow)
			return
		}

		// Create capturer with server
		u.capturer = capture.NewCapturer(
			u.servers[serverIdx],
			u.interfaces[ifaceIdx].Name,
			u.onPacketCaptured,
		)
	}

	// Start capture
	err := u.capturer.Start(filename)
	if err != nil {
		dialog.ShowError(fmt.Errorf("Failed to start capture: %v", err), u.mainWindow)
		return
	}

	// Update UI
	u.isCapturing = true
	u.startButton.Disable()
	u.stopButton.Enable()

	// Update status bar based on capture mode
	if usingProfile {
		// Show profile info in status bar
		portInfo := ""
		if len(u.selectedProfile.Ports) > 0 || len(u.selectedProfile.PortRanges) > 0 {
			portInfo = " with port filters"
		}

		ipInfo := ""
		if u.selectedProfile.IP != "" {
			ipInfo = " - " + u.selectedProfile.IP
		}

		u.statusBar.SetText(fmt.Sprintf("Capturing on %s%s%s using profile: %s",
			u.interfaces[ifaceIdx].Name, ipInfo, portInfo, u.selectedProfile.Name))
	} else {
		// Show server info in status bar
		serverIdx := u.serverSelect.SelectedIndex()
		u.statusBar.SetText(fmt.Sprintf("Capturing on %s - %s",
			u.interfaces[ifaceIdx].Name, u.servers[serverIdx].IP))
	}

	// Clear packet list
	u.packets = nil
	u.packetList.Refresh()
}

// stopCapture stops packet capture
func (u *UI) stopCapture() {
	if !u.isCapturing || u.capturer == nil {
		return
	}

	// Stop capture
	err := u.capturer.Stop()
	if err != nil {
		dialog.ShowError(fmt.Errorf("Error stopping capture: %v", err), u.mainWindow)
	}

	// Update UI
	u.isCapturing = false
	u.startButton.Enable()
	u.stopButton.Disable()
	u.statusBar.SetText("Capture stopped")

	// Show capture summary
	session := u.capturer.GetSession()
	duration := session.EndTime.Sub(session.StartTime)

	dialog.ShowInformation(
		"Capture Summary",
		fmt.Sprintf(
			"Captured %d packets in %v\nSaved to: %s",
			session.PacketCount,
			duration.Round(time.Millisecond),
			session.FilePath,
		),
		u.mainWindow,
	)
}

// onPacketCaptured handles newly captured packets
func (u *UI) onPacketCaptured(packet models.Packet) {
	// Parse packet
	processedPackets, err := u.parser.ProcessPacket(packet)
	if err != nil {
		fmt.Printf("Error processing packet: %v\n", err)
		return
	}

	// Add to packet list
	for _, p := range processedPackets {
		u.packets = append(u.packets, p)

		// Update type filter options if needed
		u.updateTypeFilter(p.RequestType)
	}

	// Refresh UI
	u.packetList.Refresh()
}

// onPacketSelected handles packet selection
func (u *UI) onPacketSelected(id widget.ListItemID) {
	if id >= len(u.packets) {
		return
	}

	u.selectedPacket = &u.packets[id]
	u.displayPacketDetails(*u.selectedPacket)
}

// displayPacketDetails shows detailed packet information
func (u *UI) displayPacketDetails(packet models.Packet) {
	// Xử lý dữ liệu packet (decrypt và decompress nếu cần)
	var processedData []byte
	var err error
	var interpretationText string

	// Kiểm tra nếu cần decrypt
	if u.decryptPackets {
		// Xử lý packet với decrypt và decompress nếu cần
		processedData, err = utils.ProcessPacket(packet.RawData, true)
		if err != nil {
			interpretationText = fmt.Sprintf("Error processing packet: %v", err)
			processedData = packet.RawData // Sử dụng dữ liệu gốc nếu có lỗi
		} else {
			// Phân tích thông tin packet
			interpretationText = analyzePacket(processedData)
		}
	} else {
		// Sử dụng dữ liệu gốc
		processedData = packet.RawData
		interpretationText = "Enable 'Decrypt Packets' to see decoded data"
	}

	// Format hex data
	hexData := parser.FormatHexData(processedData)

	// Update hex view
	u.hexView.Segments = nil

	for _, hd := range hexData {
		// Address
		u.hexView.Segments = append(u.hexView.Segments,
			&widget.TextSegment{
				Text:  hd.Address + "  ",
				Style: widget.RichTextStyle{TextStyle: fyne.TextStyle{}},
			})

		// Hex values
		u.hexView.Segments = append(u.hexView.Segments,
			&widget.TextSegment{
				Text:  hd.HexStr + "  ",
				Style: widget.RichTextStyle{TextStyle: fyne.TextStyle{Bold: true}},
			})

		// ASCII representation
		u.hexView.Segments = append(u.hexView.Segments,
			&widget.TextSegment{
				Text:  hd.ByteStr + "\n",
				Style: widget.RichTextStyle{TextStyle: fyne.TextStyle{}},
			})
	}

	u.hexView.Refresh()

	// Update data interpretation
	u.dataInterpretation.SetText(interpretationText)
}

// analyzePacket phân tích packet để hiển thị thông tin chi tiết
func analyzePacket(data []byte) string {
	if len(data) < 8 {
		return "Packet quá ngắn để phân tích"
	}

	// Kiểm tra header và footer
	if data[0] != 0xaa || data[1] != 0x55 {
		return "Không phải packet hợp lệ (header không đúng)"
	}

	// Lấy thông tin cơ bản
	packetLength := binary.LittleEndian.Uint16(data[2:4])
	sessionID := binary.LittleEndian.Uint16(data[4:6])

	var opcode uint16
	if len(data) >= 10 {
		opcode = binary.LittleEndian.Uint16(data[8:10])
	}

	// Tạo thông tin chi tiết
	var details strings.Builder
	details.WriteString(fmt.Sprintf("Packet Length: %d bytes\n", packetLength))
	details.WriteString(fmt.Sprintf("Session ID: %d\n", sessionID))
	details.WriteString(fmt.Sprintf("Opcode: 0x%04X (%d)\n", opcode, opcode))

	// Kiểm tra nếu là packet nén
	if sessionID > 0 && sessionID < 100 {
		details.WriteString("Type: Compressed packet\n")
	}

	// Thêm thông tin khác nếu cần

	return details.String()
}

// applyFilter applies the user-specified filter
func (u *UI) applyFilter() {
	filterText := u.filterEntry.Text
	if filterText == "" {
		return
	}

	// Parse filter types
	var types []int
	for _, typeStr := range strings.Split(filterText, ",") {
		typeStr = strings.TrimSpace(typeStr)
		if typeStr == "" {
			continue
		}

		typeInt, err := strconv.Atoi(typeStr)
		if err != nil {
			continue
		}

		types = append(types, typeInt)
	}

	// Apply filter
	if len(types) == 0 {
		return
	}

	dialog.ShowInformation("Filter Applied",
		fmt.Sprintf("Filtering for packet types: %v", types),
		u.mainWindow)
}

// filterByType filters packets by type
func (u *UI) filterByType(typeStr string) {
	if typeStr == "All" {
		// Show all packets
		return
	}

	// Parse type
	typeInt, err := strconv.Atoi(typeStr)
	if err != nil {
		return
	}

	dialog.ShowInformation("Type Filter Applied",
		fmt.Sprintf("Showing only packets of type: %d", typeInt),
		u.mainWindow)
}

// updateTypeFilter updates the type filter dropdown
func (u *UI) updateTypeFilter(newType int) {
	// Check if type already exists in options
	typeStr := strconv.Itoa(newType)
	for _, opt := range u.typeSelect.Options {
		if opt == typeStr {
			return
		}
	}

	// Add new type to options
	options := append(u.typeSelect.Options, typeStr)
	u.typeSelect.Options = options
}
