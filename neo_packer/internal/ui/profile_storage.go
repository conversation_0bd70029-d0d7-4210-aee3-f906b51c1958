package ui

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"fyne.io/fyne/v2/dialog"

	"1l4e/neo_packer/internal/models"
)

const (
	profilesFileName = "capture_profiles.json"
)

// saveProfiles saves the capture profiles to a JSON file
func (u *UI) saveProfiles() {
	// Create profiles directory if it doesn't exist
	profilesDir := filepath.Join(".", "profiles")
	if err := os.Mkdir<PERSON>ll(profilesDir, 0755); err != nil {
		dialog.ShowError(fmt.Errorf("Failed to create profiles directory: %v", err), u.mainWindow)
		return
	}

	// Marshal profiles to JSON
	data, err := json.MarshalIndent(u.profiles, "", "  ")
	if err != nil {
		dialog.ShowError(fmt.Errorf("Failed to marshal profiles: %v", err), u.mainWindow)
		return
	}

	// Write to file
	profilesPath := filepath.Join(profilesDir, profilesFileName)
	if err := os.WriteFile(profilesPath, data, 0644); err != nil {
		dialog.ShowError(fmt.Errorf("Failed to write profiles to file: %v", err), u.mainWindow)
		return
	}

	dialog.ShowInformation("Profiles Saved", 
		fmt.Sprintf("Saved %d profiles to %s", len(u.profiles), profilesPath), 
		u.mainWindow)
}

// loadProfiles loads capture profiles from a JSON file
func (u *UI) loadProfiles() {
	// Check if profiles file exists
	profilesDir := filepath.Join(".", "profiles")
	profilesPath := filepath.Join(profilesDir, profilesFileName)
	
	if _, err := os.Stat(profilesPath); os.IsNotExist(err) {
		// File doesn't exist, create default profiles
		u.createDefaultProfiles()
		return
	}

	// Read profiles file
	data, err := os.ReadFile(profilesPath)
	if err != nil {
		dialog.ShowError(fmt.Errorf("Failed to read profiles file: %v", err), u.mainWindow)
		u.createDefaultProfiles()
		return
	}

	// Unmarshal profiles
	var profiles []models.CaptureProfile
	if err := json.Unmarshal(data, &profiles); err != nil {
		dialog.ShowError(fmt.Errorf("Failed to parse profiles file: %v", err), u.mainWindow)
		u.createDefaultProfiles()
		return
	}

	// Set profiles
	u.profiles = profiles
	u.updateProfileSelect()
}

// createDefaultProfiles creates default capture profiles
func (u *UI) createDefaultProfiles() {
	// Create some example profiles
	u.profiles = []models.CaptureProfile{
		{
			ID:          1,
			Name:        "HTTP/HTTPS",
			Description: "Capture HTTP and HTTPS traffic",
			IP:          "",
			Ports:       []int{80, 443},
			PortRanges:  []models.PortRange{},
			IsActive:    false,
		},
		{
			ID:          2,
			Name:        "Database",
			Description: "Capture common database ports",
			IP:          "",
			Ports:       []int{1433, 3306, 5432, 27017},
			PortRanges:  []models.PortRange{},
			IsActive:    false,
		},
		{
			ID:          3,
			Name:        "Dynamic Ports",
			Description: "Capture traffic on dynamic/private ports",
			IP:          "",
			Ports:       []int{},
			PortRanges: []models.PortRange{
				{Start: 49152, End: 65535},
			},
			IsActive: false,
		},
	}

	u.updateProfileSelect()
}
