package capture

import (
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"1l4e/neo_packer/internal/models"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
	"github.com/google/gopacket/pcap"
	"github.com/google/gopacket/pcapgo"
)

// Capturer handles packet capturing functionality
type Capturer struct {
	handle         *pcap.Handle
	pcapWriter     *pcapgo.Writer
	file           *os.File
	server         models.Server
	captureProfile *models.CaptureProfile // Optional capture profile
	interfaceName  string
	filter         string
	stopChan       chan struct{}
	wg             sync.WaitGroup
	packetCount    int
	startTime      time.Time
	endTime        time.Time
	onPacket       func(packet models.Packet)
}

// NewCapturer creates a new packet capturer
func NewCapturer(server models.Server, interfaceName string, onPacket func(packet models.Packet)) *Capturer {
	return &Capturer{
		server:        server,
		interfaceName: interfaceName,
		stopChan:      make(chan struct{}),
		onPacket:      onPacket,
	}
}

// NewCapturerWithProfile creates a new packet capturer with a capture profile
func NewCapturerWithProfile(profile models.CaptureProfile, interfaceName string, onPacket func(packet models.Packet)) *Capturer {
	// Create a dummy server for compatibility
	server := models.Server{
		ID:   0,
		Name: profile.Name,
		IP:   profile.IP,
	}

	return &Capturer{
		server:         server,
		captureProfile: &profile,
		interfaceName:  interfaceName,
		stopChan:       make(chan struct{}),
		onPacket:       onPacket,
	}
}

// Start begins capturing packets
func (c *Capturer) Start(outputPath string) error {
	var err error

	// Open output file
	c.file, err = os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("error creating pcap file: %v", err)
	}

	// Create pcap writer
	c.pcapWriter = pcapgo.NewWriter(c.file)
	err = c.pcapWriter.WriteFileHeader(65536, layers.LinkTypeEthernet)
	if err != nil {
		c.file.Close()
		return fmt.Errorf("error writing pcap header: %v", err)
	}

	// Open device
	inactive, err := pcap.NewInactiveHandle(c.interfaceName)
	if err != nil {
		c.file.Close()
		return fmt.Errorf("error creating inactive handle: %v", err)
	}
	defer inactive.CleanUp()

	// Set options
	if err = inactive.SetSnapLen(65536); err != nil {
		c.file.Close()
		return fmt.Errorf("error setting snap length: %v", err)
	}
	if err = inactive.SetPromisc(true); err != nil {
		c.file.Close()
		return fmt.Errorf("error setting promiscuous mode: %v", err)
	}
	if err = inactive.SetTimeout(time.Second); err != nil {
		c.file.Close()
		return fmt.Errorf("error setting timeout: %v", err)
	}

	// Activate handle
	c.handle, err = inactive.Activate()
	if err != nil {
		c.file.Close()
		return fmt.Errorf("error activating handle: %v", err)
	}

	// Set filter
	if c.captureProfile != nil {
		// Use profile-based filter
		c.filter = CreateFilterFromProfile(*c.captureProfile)
	} else if c.server.IsLocal {
		// Use server-based filter (legacy)
		c.filter = fmt.Sprintf("host %s and (port %d or port %d)",
			c.server.IP, c.server.TargetPort, c.server.LoginPort)
	} else {
		c.filter = fmt.Sprintf("host %s", c.server.IP)
	}

	if err = c.handle.SetBPFFilter(c.filter); err != nil {
		c.handle.Close()
		c.file.Close()
		return fmt.Errorf("error setting BPF filter: %v", err)
	}

	// Start capture
	c.startTime = time.Now()
	c.packetCount = 0
	c.wg.Add(1)

	go c.capturePackets()

	return nil
}

// Stop stops capturing packets
func (c *Capturer) Stop() error {
	close(c.stopChan)
	c.wg.Wait()
	c.endTime = time.Now()

	if c.handle != nil {
		c.handle.Close()
		c.handle = nil
	}

	if c.file != nil {
		err := c.file.Close()
		c.file = nil
		return err
	}

	return nil
}

// GetSession returns information about the current capture session
func (c *Capturer) GetSession() models.CaptureSession {
	return models.CaptureSession{
		StartTime:     c.startTime,
		EndTime:       c.endTime,
		ServerInfo:    c.server,
		InterfaceName: c.interfaceName,
		PacketCount:   c.packetCount,
		FilePath:      c.file.Name(),
	}
}

// capturePackets is the main packet processing loop
func (c *Capturer) capturePackets() {
	defer c.wg.Done()

	packetSource := gopacket.NewPacketSource(c.handle, c.handle.LinkType())
	packetChan := packetSource.Packets()

	for {
		select {
		case <-c.stopChan:
			return
		case packet := <-packetChan:
			if packet == nil {
				continue
			}

			// Write packet to file
			err := c.pcapWriter.WritePacket(packet.Metadata().CaptureInfo, packet.Data())
			if err != nil {
				fmt.Printf("Error writing packet: %v\n", err)
				continue
			}

			c.packetCount++

			// Process TCP packets
			if tcpLayer := packet.Layer(layers.LayerTypeTCP); tcpLayer != nil {
				tcp, _ := tcpLayer.(*layers.TCP)

				// Process IP layer
				var srcIP, dstIP string
				if ipv4Layer := packet.Layer(layers.LayerTypeIPv4); ipv4Layer != nil {
					ip, _ := ipv4Layer.(*layers.IPv4)
					srcIP = ip.SrcIP.String()
					dstIP = ip.DstIP.String()
				} else if ipv6Layer := packet.Layer(layers.LayerTypeIPv6); ipv6Layer != nil {
					ip, _ := ipv6Layer.(*layers.IPv6)
					srcIP = ip.SrcIP.String()
					dstIP = ip.DstIP.String()
				}

				// Process application data
				var payload []byte
				if appLayer := packet.ApplicationLayer(); appLayer != nil {
					payload = appLayer.Payload()
				}

				// Skip if no payload
				if len(payload) == 0 {
					continue
				}

				// Determine direction
				direction := ">>>"
				if tcp.DstPort == layers.TCPPort(c.server.TargetPort) {
					direction = ">>>"
				} else {
					direction = "<"
				}

				// Create packet model
				p := models.Packet{
					Timestamp:   packet.Metadata().Timestamp,
					SrcIP:       srcIP,
					SrcPort:     int(tcp.SrcPort),
					DstIP:       dstIP,
					DstPort:     int(tcp.DstPort),
					Direction:   direction,
					RawData:     payload,
					RequestType: 0, // Will be filled by parser
					WorldID:     0, // Will be filled by parser
				}

				// Call packet handler
				if c.onPacket != nil {
					c.onPacket(p)
				}
			}
		}
	}
}

// CreateFilterFromProfile creates a BPF filter string from a capture profile
func CreateFilterFromProfile(profile models.CaptureProfile) string {
	var portFilters []string

	// Add individual ports
	for _, port := range profile.Ports {
		portFilters = append(portFilters, fmt.Sprintf("port %d", port))
	}

	// Add port ranges
	for _, portRange := range profile.PortRanges {
		if portRange.Start == portRange.End {
			portFilters = append(portFilters, fmt.Sprintf("port %d", portRange.Start))
		} else {
			// BPF syntax for port range: portrange start-end
			portFilters = append(portFilters, fmt.Sprintf("portrange %d-%d", portRange.Start, portRange.End))
		}
	}

	// Combine port filters with OR
	var portFilter string
	if len(portFilters) > 0 {
		portFilter = fmt.Sprintf("(%s)", strings.Join(portFilters, " or "))
	}

	// Create final filter
	if profile.IP != "" && portFilter != "" {
		return fmt.Sprintf("host %s and %s", profile.IP, portFilter)
	} else if profile.IP != "" {
		return fmt.Sprintf("host %s", profile.IP)
	} else if portFilter != "" {
		return portFilter
	}

	// Default to empty filter (capture all)
	return ""
}

// GetInterfaces returns a list of available network interfaces
func GetInterfaces() ([]models.NetworkInterface, error) {
	devices, err := pcap.FindAllDevs()
	if err != nil {
		return nil, err
	}

	var interfaces []models.NetworkInterface
	for _, device := range devices {
		isUp := false
		// Kiểm tra nếu flag "up" được thiết lập trong device.Flags
		// device.Flags là một uint32, không phải là một slice hoặc map
		// Thường thì flag "up" được biểu diễn bằng bit 0x1
		if device.Flags&0x1 != 0 {
			isUp = true
		}

		iface := models.NetworkInterface{
			Name:        device.Name,
			Description: device.Description,
			IsUp:        isUp,
		}
		interfaces = append(interfaces, iface)
	}

	return interfaces, nil
}
