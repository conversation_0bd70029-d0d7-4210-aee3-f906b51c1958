package utils

import (
	"bytes"
	"compress/zlib"
	"encoding/binary"
	"fmt"
	"io"
)

// DecompressZlibPacket giải nén một packet đã được nén bằng zlib
func DecompressZlibPacket(data []byte) ([]byte, error) {
	if len(data) < 10 {
		return nil, fmt.<PERSON><PERSON><PERSON>("DecompressZlibPacket Error: packet quá ngắn, độ dài: %d bytes", len(data))
	}

	// Kiểm tra header và footer
	if data[0] != 0xaa || data[1] != 0x55 {
		return nil, fmt.Errorf("DecompressZlibPacket Error: packet header không hợp lệ: %02X-%02X, cần: AA-55",
			data[0], data[1])
	}

	if data[len(data)-2] != 0x55 || data[len(data)-1] != 0xaa {
		return nil, fmt.<PERSON><PERSON><PERSON>("DecompressZlibPacket Error: packet footer không hợp lệ: %02X-%02X, cần: 55-AA",
			data[len(data)-2], data[len(data)-1])
	}

	// Lấy sessionID từ byte thứ 4-5
	sessionID := binary.LittleEndian.Uint16(data[4:6])

	// Kiểm tra nếu sessionID > 0 và < 100 thì đây là packet nén
	if sessionID <= 0 || sessionID >= 100 {
		return data, nil // Không phải packet nén, trả về nguyên bản
	}

	// Lấy dữ liệu nén (bỏ qua header 6 byte và footer 2 byte)
	compressedData := data[6 : len(data)-2]

	// Tạo reader zlib để giải nén
	zlibReader, err := zlib.NewReader(bytes.NewReader(compressedData))
	if err != nil {
		return nil, fmt.Errorf("DecompressZlibPacket Error: không thể tạo zlib reader: %v", err)
	}
	defer zlibReader.Close()

	// Đọc dữ liệu đã giải nén
	var decompressedBuffer bytes.Buffer
	_, err = io.Copy(&decompressedBuffer, zlibReader)
	if err != nil {
		return nil, fmt.Errorf("DecompressZlibPacket Error: không thể giải nén dữ liệu: %v", err)
	}

	decompressedData := decompressedBuffer.Bytes()

	// Tạo packet mới với dữ liệu đã giải nén
	packetNew := make([]byte, len(decompressedData)+6)
	copy(packetNew[:2], data[:2])                                                // Copy header AA55
	binary.LittleEndian.PutUint16(packetNew[2:4], uint16(len(decompressedData))) // Cập nhật độ dài
	copy(packetNew[4:4+len(decompressedData)], decompressedData)                 // Copy dữ liệu đã giải nén
	copy(packetNew[len(packetNew)-2:], data[len(data)-2:])                       // Copy footer 55AA

	return packetNew, nil
}

// IsCompressedPacket kiểm tra xem packet có phải là packet nén không
func IsCompressedPacket(data []byte) bool {
	if len(data) < 6 {
		return false
	}

	// Kiểm tra header
	if data[0] != 0xaa || data[1] != 0x55 {
		return false
	}

	// Lấy sessionID từ byte thứ 4-5
	sessionID := binary.LittleEndian.Uint16(data[4:6])

	// Kiểm tra nếu sessionID > 0 và < 100 thì đây là packet nén
	return sessionID > 0 && sessionID < 100
}

// ProcessPacket xử lý packet: giải mã và giải nén nếu cần
func ProcessPacket(data []byte, decrypt bool) ([]byte, error) {
	if len(data) < 6 {
		return data, nil
	}

	// Kiểm tra header và footer
	if data[0] != 0xaa || data[1] != 0x55 || data[len(data)-2] != 0x55 || data[len(data)-1] != 0xaa {
		return data, nil // Không phải packet hợp lệ, trả về nguyên bản
	}

	processedData := data
	var err error

	// Bước 1: Giải mã nếu được yêu cầu
	if decrypt {
		crypto := NewPacketCrypto()
		processedData, err = crypto.DecryptPacket(data)
		if err != nil {
			return data, fmt.Errorf("ProcessPacket Error: không thể giải mã: %v", err)
		}
	}

	// Bước 2: Kiểm tra và giải nén nếu cần
	sessionID := binary.LittleEndian.Uint16(processedData[4:6])
	if sessionID > 0 && sessionID < 100 {
		// Thử giải nén với DecompressPacket từ compress.go
		processedData, err = DecompressPacket(processedData)
		if err != nil {
			// Nếu không thành công, thử với DecompressZlibPacket
			processedData, err = DecompressZlibPacket(processedData)
			if err != nil {
				return processedData, fmt.Errorf("ProcessPacket Error: không thể giải nén: %v", err)
			}
		}
	}

	return processedData, nil
}
