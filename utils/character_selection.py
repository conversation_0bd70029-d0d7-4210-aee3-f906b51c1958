import tkinter as tk

class CharacterSelectWindow:
    def __init__(self, main_window):
        self.main_window = main_window
        self.window = tk.Toplevel(main_window)
        self.window.title("Character Select")

        self.character_list = tk.Listbox(self.window)
        self.character_list.pack()

        for character in self.main_window.characters:
            self.character_list.insert(tk.END, character.name)

        self.select_button = tk.Button(self.window, text="Select", command=self.select_character)
        self.select_button.pack()

    def select_character(self):
        selected_index = self.character_list.curselection()[0]
        selected_character = self.main_window.characters[selected_index]
        self.main_window.show_character_info(selected_character)
        self.main_window.change_map(selected_character.map)
        self.window.destroy()

class MainWindow:
    def __init__(self, root):
        self.root = root
        self.characters = []
        self.current_map = None

        self.control_frame = tk.Frame(self.root)
        self.control_frame.pack()

        self.open_character_select_button = tk.But<PERSON>(self.root, text="Open Character Select", command=self.open_character_select)
        self.open_character_select_button.pack()

    def open_character_select(self):
        character_select_window = CharacterSelectWindow(self)

    def show_character_info(self, character):
        self.control_frame.destroy()
        self.control_frame = tk.Frame(self.root)
        self.control_frame.pack()

        name_label = tk.Label(self.control_frame, text="Name: " + character.name)
        name_label.pack()

        level_label = tk.Label(self.control_frame, text="Level: " + str(character.level))
        level_label.pack()

        map_label = tk.Label(self.control_frame, text="Map: " + character.map.name)
        map_label.pack()

    def change_map(self, map):
        self.current_map = map

root = tk.Tk()
main_window = MainWindow(root)
root.mainloop()