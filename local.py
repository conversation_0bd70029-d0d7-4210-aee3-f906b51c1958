from datetime import datetime
import sqlite3
import tkinter as tk
from tkinter import ttk
from tkinter.scrolledtext import ScrolledText
from scapy.all import sniff, IP, TCP, Raw
import threading
from tkinter import StringVar
import json
from encrypt import decrypt_data
from utils.character_info import handle_character_info_17
from utils.map import show_map_image
from utils.map_decode import save_map_data
from utils.util_fn import manage_packet,get_type,decode_packet

#target_ip = "**************"
#target_ip = "************" #channel 2
#target_ip = "************" #channel 8
target_ip = '127.0.0.1'  # Localhost IP
target_port = 13001
login_port = 1756
#target_ip = "**************"
# Global variable to control the sniffing thread
sniff_thread = None
sniffing = False
stop_sniffing = threading.Event()
fld_map = 101

with open('data.json', 'r') as file:
    data = json.load(file)
# Initialize SQLite database and create table if not exists
def initialize_db():
    conn = sqlite3.connect('packet_local.db')
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS packets (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TEXT,
            src_ip TEXT,
            src_port INTEGER,
            dst_ip TEXT,
            dst_port INTEGER,
            request_type INTEGER,
            direction TEXT,
            raw_data_hex TEXT,
            raw_data_bytes BLOB,
            wordid INTEGER
        )
    ''')
    conn.commit()
    conn.close()

initialize_db()

def clear_database():
    conn = sqlite3.connect('packet_local.db')
    cursor = conn.cursor()
    cursor.execute("DELETE FROM packets")
    conn.commit()
    conn.close()
    # Clear the Treeview
    packets_tree.delete(*packets_tree.get_children())
    # Reset the type combobox
    type_combobox['values'] = ("All",)
    selected_type.set("All")
    print("Database cleared and GUI updated.")

# Function to manage packet data


# Function to populate the packets table
def populate_packets_table():
    packets_tree.delete(*packets_tree.get_children())  # Clear existing entries
    conn = sqlite3.connect('packet_local.db')
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM packets ")
    rows = cursor.fetchall()
    conn.close()

    for row in rows:
        packets_tree.insert("", 0, values=row)

def filter_by_type(selected_type):
    selected_type_str = str(selected_type).strip()  # Ensure selected_type is a string and strip spaces
    filtered_data = {}
    for k, v in data.items():
        key_type = k.split(",")[0].strip()  # Extract and strip the type part of the key
        if key_type == selected_type_str:
            filtered_data[k] = v
    options = [f"{k}: {v['name']}" for k, v in filtered_data.items()]
    return options

# Function to show packet details
def show_packet_details(event):
    selected_item = packets_tree.selection()[0]
    packet_id = packets_tree.item(selected_item, "values")[0]

    # Query the database to get the packet details
    conn = sqlite3.connect('packet_local.db')
    cursor = conn.cursor()
    cursor.execute("SELECT raw_data_hex, raw_data_bytes FROM packets WHERE id=?", (packet_id,))
    packet = cursor.fetchone()
    conn.close()

    hex_data_text.delete("1.0", tk.END)

    if packet:
        hex_data, byte_data = packet
        if hex_data and byte_data:
            formatted_hex = format_hex_data(hex_data)
            request_type,wordid = manage_packet(byte_data)
            world_label.config(text=f"WorldID : {wordid}")
            typehex_label.config(text=f"Type : {request_type} - {filter_by_type(request_type)}")
            hex_data_text.insert(tk.END, formatted_hex)
            root.clipboard_clear()
            root.clipboard_append(hex_data)
            root.update()
            sync_scroll('moveto', 0)  # Scroll to the top

def format_hex_data(hex_data):
    hex_lines = []
    for i in range(0, len(hex_data), 32):
        hex_chunk = hex_data[i:i+32]
        hex_str = " ".join(hex_chunk[j:j+2] for j in range(0, len(hex_chunk), 2))
        address = f"{i:08X}"

        # Convert hex to byte data
        byte_data = bytearray.fromhex(hex_chunk)
        byte_str = "".join(chr(b) if 32 <= b <= 126 else '.' for b in byte_data)

        # Format the line to include both hex and byte data
        hex_lines.append(f"{address} {hex_str:<47} {byte_str}")

    return "\n".join(hex_lines)



# Function to stop packet capture
def stop_capture():
    global sniffing, stop_sniffing
    if sniffing:
        stop_sniffing.set()  # Signal the sniffing thread to stop
        sniffing = False
        status_label.config(text="Status: Stopped")

def sync_scroll(*args):
    hex_data_text.yview(*args)

# GUI Setup
root = tk.Tk()
root.title("Packet Capture")
# Frame for IP input and control buttons
control_frame = tk.Frame(root)
control_frame.pack(side=tk.TOP, fill=tk.X)

character_frame = tk.Frame(root)
character_frame.pack(side=tk.BOTTOM, fill=tk.X)

# IP input field
ip_label = tk.Label(control_frame, text="Target IP:")
ip_label.pack(side=tk.LEFT, padx=5, pady=5)
ip_entry = tk.Entry(control_frame)
ip_entry.pack(side=tk.LEFT, padx=5, pady=5)

# Start and Stop buttons
start_button = tk.Button(control_frame, text="Start Capture", command=lambda: start_capture(ip_entry.get() if ip_entry.get() else target_ip))
start_button.pack(side=tk.LEFT, padx=5, pady=5)
stop_button = tk.Button(control_frame, text="Stop Capture", command=stop_capture)
stop_button.pack(side=tk.LEFT, padx=5, pady=5)


# Status label
status_label = tk.Label(control_frame, text="Status: Stopped")
status_label.pack(side=tk.LEFT, padx=5, pady=5)

fld_name = ""
fld_level = ""
#add character info
character_name_label = tk.Label(character_frame,text=f"Name : {fld_name}")
character_name_label.pack(side=tk.LEFT,padx=5,pady=5)
character_level_label = tk.Label(character_frame,text=f"Level : {fld_level}")
character_level_label.pack(side=tk.LEFT,padx=5,pady=5)
current_map_label = tk.Label(character_frame,text=f"Map : {fld_map}")
current_map_label.pack(side=tk.LEFT,padx=5,pady=5)
# Add Clear Button
clear_button = tk.Button(control_frame, text="Clear Database", command=clear_database)
clear_button.pack(side=tk.LEFT, padx=5, pady=5)
# Filter request type checkbox
#filter_var = tk.BooleanVar(value=False)
#filter_checkbox = tk.Checkbutton(control_frame, text="Filter by Request Type", variable=filter_var)
#filter_checkbox.pack(side=tk.LEFT, padx=5, pady=5)

# Left Frame for packets table
left_frame = tk.Frame(root)
left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

# Right Frame for packet details
right_frame = tk.Frame(root)
right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

selected_type = StringVar()
selected_type.set("All")

# Add Combobox for packet types
type_label = tk.Label(control_frame, text="Filter by Type:")
type_label.pack(side=tk.LEFT, padx=5, pady=5)

type_combobox = ttk.Combobox(control_frame, textvariable=selected_type, state="readonly")
type_combobox.pack(side=tk.LEFT, padx=5, pady=5)
type_combobox['values'] = ("All",)  # Initialize with "All" option




def initial_type_combobox():
    conn = sqlite3.connect('packet_local.db')
    cursor = conn.cursor()
    cursor.execute("SELECT DISTINCT request_type FROM packets")
    packets = cursor.fetchall()
    conn.close()

    current_types = type_combobox['values']

    # Convert current_types to integers, ignoring the "All" option
    current_types_int = [int(t) for t in current_types if t != "All"]

    if packets:
        new_types = sorted([col[0] for col in packets if col[0] is not None and col[0] not in current_types_int])
        type_combobox["values"] = tuple(["All"] + sorted(current_types_int + new_types))

initial_type_combobox()



def update_type_combobox(new_type):
    if new_type is not None:
        current_types = type_combobox['values']

        # Convert current_types to integers, ignoring the "All" option
        current_types_int = [int(t) for t in current_types if t != "All"]

        if new_type not in current_types_int:
            type_combobox['values'] = tuple(["All"] + sorted(current_types_int + [new_type]))



# Add event handler for combobox selection
def on_type_selected(event):
    filter_packets_by_type(selected_type.get())

type_combobox.bind("<<ComboboxSelected>>", on_type_selected)

# Treeview for packets
columns = ("ID", "Time", "Source IP", "S.Port", "Des IP", "D.Port", "Type", "Side")
packets_tree = ttk.Treeview(left_frame, columns=columns, show="headings")

# Define tags for coloring rows
packets_tree.tag_configure('Send', background='red')
packets_tree.tag_configure('Recv', background='green')

for col in columns:
    packets_tree.heading(col, text=col)
    packets_tree.column(col, width=50 if col in ["S.Port", "D.Port", "ID", "Type", "Side"] else 120 if col == "Time" else 100)  # Adjust column width

packets_tree.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)

# Scrollbars for Treeview
scroll_y = tk.Scrollbar(left_frame, orient=tk.VERTICAL, command=packets_tree.yview)
scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
packets_tree.configure(yscroll=scroll_y.set)

scroll_x = tk.Scrollbar(left_frame, orient=tk.HORIZONTAL, command=packets_tree.xview)
scroll_x.pack(side=tk.BOTTOM, fill=tk.X)
packets_tree.configure(xscroll=scroll_x.set)

# Bind selection event to show details
packets_tree.bind("<<TreeviewSelect>>", show_packet_details)

# Frame for Hex and Byte Data
right_frame_top = tk.Frame(right_frame)
right_frame_top.pack(side=tk.TOP,fill=tk.BOTH,expand=True)
right_frame_bottom= tk.Frame(right_frame)
right_frame_bottom.pack(side=tk.BOTTOM,fill=tk.BOTH,expand=True)

world_label = tk.Label(right_frame_top, text="World ID: ")
world_label.pack(side=tk.LEFT,padx=5,pady=5)

typehex_label = tk.Label(right_frame_top, text="Type ID: ")
typehex_label.pack(side=tk.LEFT,padx=5,pady=5)

data_frame = tk.Frame(right_frame_bottom)
data_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True)

# ScrolledText for Hex and Byte Data
hex_data_text = ScrolledText(data_frame, wrap=tk.WORD, width=100)
hex_data_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

# Synchronized scrolling function (only one text widget now)
def sync_scroll(*args):
    hex_data_text.yview(*args)

hex_data_text.config(yscrollcommand=sync_scroll)
scroll_y = tk.Scrollbar(data_frame, orient=tk.VERTICAL, command=sync_scroll)
scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
hex_data_text.config(yscrollcommand=scroll_y.set)

# Populate packets table
populate_packets_table()



def filter_packets_by_type(packet_type):
    packets_tree.delete(*packets_tree.get_children())  # Clear existing entries
    conn = sqlite3.connect('packet_local.db')
    cursor = conn.cursor()

    if packet_type == "All":
        cursor.execute("SELECT * FROM packets ORDER BY timestamp DESC")
    else:
        cursor.execute("SELECT * FROM packets WHERE request_type=? ORDER BY timestamp DESC", (packet_type,))
    
    rows = cursor.fetchall()
    conn.close()

    for row in rows:
        packets_tree.insert("", tk.END, values=row)


# Function to handle packet capturing

def insert_buffer(timestamp,ip_layer,tcp_layer,request_type,direction,wordid,buffer):
    conn = sqlite3.connect("packet_local.db")
    cursor = conn.cursor()
    cursor.execute(
        """
        INSERT INTO packets (timestamp, src_ip, src_port, dst_ip, dst_port, request_type, direction, raw_data_hex, raw_data_bytes, wordid)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """,
        (
            timestamp,
            ip_layer.src,
            tcp_layer.sport,
            ip_layer.dst,
            tcp_layer.dport,
            request_type,
            direction,
            buffer.hex(),
            buffer,
            wordid,
        ),
    )
    conn.commit()
    last_id = cursor.lastrowid
    conn.close()
    return last_id

buffer = b""

def packet_callback(packet):
    global buffer, fld_map
    if packet.haslayer(IP) and packet.haslayer(TCP) and packet.haslayer(Raw):
        ip_layer = packet.getlayer(IP)
        tcp_layer = packet.getlayer(TCP)
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if ip_layer.src == target_ip or ip_layer.dst == target_ip:
           if (tcp_layer.sport == login_port or tcp_layer.dport == login_port or tcp_layer.sport == target_port or tcp_layer.dport == target_port):
            direction = "<" if tcp_layer.sport == target_port else ">>>"
            
            raw_data = packet[Raw].load
            data_bytes = bytes(raw_data)
            buffer += data_bytes
            
            while True:
                # Find the end of the packet marked by 55AA
                end_index = buffer.find(b'\x55\xAA')
                if end_index == -1:
                    break
                
                # Find the start of the packet marked by AA55
                start_index = buffer.find(b'\xAA\x55')
                if start_index == -1 or start_index > end_index:
                    break

                # Extract the packet data
                sub_packet = buffer[start_index:end_index+2]
                buffer = buffer[end_index+2:]
                
                sub_packet= decrypt_data(sub_packet)
                # Process the sub_packet
                request_type, wordid = manage_packet(sub_packet)
 

                update_type_combobox(request_type)
                # Save data to database
                last_id = insert_buffer(timestamp, ip_layer, tcp_layer, request_type, direction, wordid, sub_packet)
                # Update GUI
                tag = 'send' if direction == "Send" else 'recv'
                if selected_type.get() in ("All", request_type):
                    if request_type in [176,128]:
                        print("Ping")
                        return
                    packets_tree.insert(
                        "",
                        0,
                        values=(
                            last_id,
                            timestamp,
                            ip_layer.src,
                            tcp_layer.sport,
                            ip_layer.dst,
                            tcp_layer.dport,
                            request_type,
                            direction,
                        ),
                        tags=(tag,)
                    )

            # Remove any remaining incomplete packet data from the buffer
            if len(buffer) > 0 and buffer[-2:] != b'\x55\xAA':
                incomplete_start = buffer.find(b'\xAA\x55')
                if incomplete_start != -1:
                    buffer = buffer[incomplete_start:]
                else:
                    buffer = b""



# Function to start packet capture
def start_capture(ip):
    global target_ip, sniffing, sniff_thread, stop_sniffing
    if not sniffing:
        target_ip = ip
        stop_sniffing.clear()
        sniff_thread = threading.Thread(target=sniff_packets)
        sniff_thread.start()
        sniffing = True
        status_label.config(text=f"Status: Capturing {target_ip}")



# Function to sniff packets
def sniff_packets():
    global stop_sniffing
    
    #bpf_filter = f"host {target_ip} and tcp"
    loopback_iface = '\\Device\\NPF_Loopback'
    bpf_filter = f"host {target_ip} and tcp"
    def stop_filter(packet):
        return stop_sniffing.is_set()

    sniff(filter=bpf_filter, iface=loopback_iface, prn=packet_callback, store=0, stop_filter=stop_filter)

# Start the GUI main loop
root.mainloop()
